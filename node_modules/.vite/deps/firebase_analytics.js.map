{"version": 3, "sources": ["../../@firebase/installations/src/util/constants.ts", "../../@firebase/installations/src/util/errors.ts", "../../@firebase/installations/src/functions/common.ts", "../../@firebase/installations/src/functions/create-installation-request.ts", "../../@firebase/installations/src/util/sleep.ts", "../../@firebase/installations/src/helpers/buffer-to-base64-url-safe.ts", "../../@firebase/installations/src/helpers/generate-fid.ts", "../../@firebase/installations/src/util/get-key.ts", "../../@firebase/installations/src/helpers/fid-changed.ts", "../../@firebase/installations/src/helpers/idb-manager.ts", "../../@firebase/installations/src/helpers/get-installation-entry.ts", "../../@firebase/installations/src/functions/generate-auth-token-request.ts", "../../@firebase/installations/src/helpers/refresh-auth-token.ts", "../../@firebase/installations/src/api/get-id.ts", "../../@firebase/installations/src/api/get-token.ts", "../../@firebase/installations/src/functions/delete-installation-request.ts", "../../@firebase/installations/src/api/delete-installations.ts", "../../@firebase/installations/src/api/on-id-change.ts", "../../@firebase/installations/src/api/get-installations.ts", "../../@firebase/installations/src/helpers/extract-app-config.ts", "../../@firebase/installations/src/functions/config.ts", "../../@firebase/installations/src/index.ts", "../../@firebase/analytics/src/constants.ts", "../../@firebase/analytics/src/logger.ts", "../../@firebase/analytics/src/errors.ts", "../../@firebase/analytics/src/helpers.ts", "../../@firebase/analytics/src/get-config.ts", "../../@firebase/analytics/src/functions.ts", "../../@firebase/analytics/src/initialize-analytics.ts", "../../@firebase/analytics/src/factory.ts", "../../@firebase/analytics/src/api.ts", "../../@firebase/analytics/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { RegisteredInstallationEntry } from '../interfaces/installation-entry';\nimport {\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\n\nexport async function deleteInstallationRequest(\n  appConfig: AppConfig,\n  installationEntry: RegisteredInstallationEntry\n): Promise<void> {\n  const endpoint = getDeleteEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n  const request: RequestInit = {\n    method: 'DELETE',\n    headers\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (!response.ok) {\n    throw await getErrorFromResponse('Delete Installation', response);\n  }\n}\n\nfunction getDeleteEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteInstallationRequest } from '../functions/delete-installation-request';\nimport { remove, update } from '../helpers/idb-manager';\nimport { RequestStatus } from '../interfaces/installation-entry';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Deletes the Firebase Installation and all associated data.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function deleteInstallations(\n  installations: Installations\n): Promise<void> {\n  const { appConfig } = installations as FirebaseInstallationsImpl;\n\n  const entry = await update(appConfig, oldEntry => {\n    if (oldEntry && oldEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n      // Delete the unregistered entry without sending a deleteInstallation request.\n      return undefined;\n    }\n    return oldEntry;\n  });\n\n  if (entry) {\n    if (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n      // Can't delete while trying to register.\n      throw ERROR_FACTORY.create(ErrorCode.DELETE_PENDING_REGISTRATION);\n    } else if (entry.registrationStatus === RequestStatus.COMPLETED) {\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      } else {\n        await deleteInstallationRequest(appConfig, entry);\n        await remove(appConfig);\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { addCallback, removeCallback } from '../helpers/fid-changed';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * An user defined callback function that gets called when Installations ID changes.\n *\n * @public\n */\nexport type IdChangeCallbackFn = (installationId: string) => void;\n/**\n * Unsubscribe a callback function previously added via {@link IdChangeCallbackFn}.\n *\n * @public\n */\nexport type IdChangeUnsubscribeFn = () => void;\n\n/**\n * Sets a new callback that will get called when Installation ID changes.\n * Returns an unsubscribe function that will remove the callback when called.\n * @param installations - The `Installations` instance.\n * @param callback - The callback function that is invoked when FID changes.\n * @returns A function that can be called to unsubscribe.\n *\n * @public\n */\nexport function onIdChange(\n  installations: Installations,\n  callback: IdChangeCallbackFn\n): IdChangeUnsubscribeFn {\n  const { appConfig } = installations as FirebaseInstallationsImpl;\n\n  addCallback(appConfig, callback);\n  return () => {\n    removeCallback(appConfig, callback);\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns an instance of {@link Installations} associated with the given\n * {@link @firebase/app#FirebaseApp} instance.\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\n *\n * @public\n */\nexport function getInstallations(app: FirebaseApp = getApp()): Installations {\n  const installationsImpl = _getProvider(app, 'installations').getImmediate();\n  return installationsImpl;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Analytics.\n */\nexport const ANALYTICS_TYPE = 'analytics';\n\n// Key to attach FID to in gtag params.\nexport const GA_FID_KEY = 'firebase_id';\nexport const ORIGIN_KEY = 'origin';\n\nexport const FETCH_TIMEOUT_MILLIS = 60 * 1000;\n\nexport const DYNAMIC_CONFIG_URL =\n  'https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig';\n\nexport const GTAG_URL = 'https://www.googletagmanager.com/gtag/js';\n\nexport const enum GtagCommand {\n  EVENT = 'event',\n  SET = 'set',\n  CONFIG = 'config',\n  CONSENT = 'consent',\n  GET = 'get'\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/analytics');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AnalyticsError {\n  ALREADY_EXISTS = 'already-exists',\n  ALREADY_INITIALIZED = 'already-initialized',\n  ALREADY_INITIALIZED_SETTINGS = 'already-initialized-settings',\n  INTEROP_COMPONENT_REG_FAILED = 'interop-component-reg-failed',\n  INVALID_ANALYTICS_CONTEXT = 'invalid-analytics-context',\n  INDEXEDDB_UNAVAILABLE = 'indexeddb-unavailable',\n  FETCH_THROTTLE = 'fetch-throttle',\n  CONFIG_FETCH_FAILED = 'config-fetch-failed',\n  NO_API_KEY = 'no-api-key',\n  NO_APP_ID = 'no-app-id',\n  NO_CLIENT_ID = 'no-client-id',\n  INVALID_GTAG_RESOURCE = 'invalid-gtag-resource'\n}\n\nconst ERRORS: ErrorMap<AnalyticsError> = {\n  [AnalyticsError.ALREADY_EXISTS]:\n    'A Firebase Analytics instance with the appId {$id} ' +\n    ' already exists. ' +\n    'Only one Firebase Analytics instance can be created for each appId.',\n  [AnalyticsError.ALREADY_INITIALIZED]:\n    'initializeAnalytics() cannot be called again with different options than those ' +\n    'it was initially called with. It can be called again with the same options to ' +\n    'return the existing instance, or getAnalytics() can be used ' +\n    'to get a reference to the already-initialized instance.',\n  [AnalyticsError.ALREADY_INITIALIZED_SETTINGS]:\n    'Firebase Analytics has already been initialized.' +\n    'settings() must be called before initializing any Analytics instance' +\n    'or it will have no effect.',\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]:\n    'Firebase Analytics Interop Component failed to instantiate: {$reason}',\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]:\n    'Firebase Analytics is not supported in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]:\n    'IndexedDB unavailable or restricted in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.FETCH_THROTTLE]:\n    'The config fetch request timed out while in an exponential backoff state.' +\n    ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [AnalyticsError.CONFIG_FETCH_FAILED]:\n    'Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}',\n  [AnalyticsError.NO_API_KEY]:\n    'The \"apiKey\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid API key.',\n  [AnalyticsError.NO_APP_ID]:\n    'The \"appId\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid app ID.',\n  [AnalyticsError.NO_CLIENT_ID]: 'The \"client_id\" field is empty.',\n  [AnalyticsError.INVALID_GTAG_RESOURCE]:\n    'Trusted Types detected an invalid gtag resource: {$gtagURL}.'\n};\n\ninterface ErrorParams {\n  [AnalyticsError.ALREADY_EXISTS]: { id: string };\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]: { reason: Error };\n  [AnalyticsError.FETCH_THROTTLE]: { throttleEndTimeMillis: number };\n  [AnalyticsError.CONFIG_FETCH_FAILED]: {\n    httpStatus: number;\n    responseMessage: string;\n  };\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]: { errorInfo: string };\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]: { errorInfo: string };\n  [AnalyticsError.INVALID_GTAG_RESOURCE]: { gtagURL: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AnalyticsError, ErrorParams>(\n  'analytics',\n  'Analytics',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { DynamicConfig, DataLayer, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GTAG_URL } from './constants';\nimport { logger } from './logger';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n// Possible parameter types for gtag 'event' and 'config' commands\ntype GtagConfigOrEventParams = ControlParams & EventParams & CustomParams;\n\n/**\n * Verifies and creates a TrustedScriptURL.\n */\nexport function createGtagTrustedTypesScriptURL(url: string): string {\n  if (!url.startsWith(GTAG_URL)) {\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_GTAG_RESOURCE, {\n      gtagURL: url\n    });\n    logger.warn(err.message);\n    return '';\n  }\n  return url;\n}\n\n/**\n * Makeshift polyfill for Promise.allSettled(). Resolves when all promises\n * have either resolved or rejected.\n *\n * @param promises Array of promises to wait for.\n */\nexport function promiseAllSettled<T>(\n  promises: Array<Promise<T>>\n): Promise<T[]> {\n  return Promise.all(promises.map(promise => promise.catch(e => e)));\n}\n\n/**\n * Creates a TrustedTypePolicy object that implements the rules passed as policyOptions.\n *\n * @param policyName A string containing the name of the policy\n * @param policyOptions Object containing implementations of instance methods for TrustedTypesPolicy, see {@link https://developer.mozilla.org/en-US/docs/Web/API/TrustedTypePolicy#instance_methods\n * | the TrustedTypePolicy reference documentation}.\n */\nexport function createTrustedTypesPolicy(\n  policyName: string,\n  policyOptions: Partial<TrustedTypePolicyOptions>\n): Partial<TrustedTypePolicy> | undefined {\n  // Create a TrustedTypes policy that we can use for updating src\n  // properties\n  let trustedTypesPolicy: Partial<TrustedTypePolicy> | undefined;\n  if (window.trustedTypes) {\n    trustedTypesPolicy = window.trustedTypes.createPolicy(\n      policyName,\n      policyOptions\n    );\n  }\n  return trustedTypesPolicy;\n}\n\n/**\n * Inserts gtag script tag into the page to asynchronously download gtag.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function insertScriptTag(\n  dataLayerName: string,\n  measurementId: string\n): void {\n  const trustedTypesPolicy = createTrustedTypesPolicy(\n    'firebase-js-sdk-policy',\n    {\n      createScriptURL: createGtagTrustedTypesScriptURL\n    }\n  );\n\n  const script = document.createElement('script');\n  // We are not providing an analyticsId in the URL because it would trigger a `page_view`\n  // without fid. We will initialize ga-id using gtag (config) command together with fid.\n\n  const gtagScriptURL = `${GTAG_URL}?l=${dataLayerName}&id=${measurementId}`;\n  (script.src as string | TrustedScriptURL) = trustedTypesPolicy\n    ? (trustedTypesPolicy as TrustedTypePolicy)?.createScriptURL(gtagScriptURL)\n    : gtagScriptURL;\n\n  script.async = true;\n  document.head.appendChild(script);\n}\n\n/**\n * Get reference to, or create, global datalayer.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function getOrCreateDataLayer(dataLayerName: string): DataLayer {\n  // Check for existing dataLayer and create if needed.\n  let dataLayer: DataLayer = [];\n  if (Array.isArray(window[dataLayerName])) {\n    dataLayer = window[dataLayerName] as DataLayer;\n  } else {\n    window[dataLayerName] = dataLayer;\n  }\n  return dataLayer;\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'config' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param measurementId GA Measurement ID to set config for.\n * @param gtagParams Gtag config params to set.\n */\nasync function gtagOnConfig(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  // If config is already fetched, we know the appId and can use it to look up what FID promise we\n  /// are waiting for, and wait only on that one.\n  const correspondingAppId = measurementIdToAppId[measurementId as string];\n  try {\n    if (correspondingAppId) {\n      await initializationPromisesMap[correspondingAppId];\n    } else {\n      // If config is not fetched yet, wait for all configs (we don't know which one we need) and\n      // find the appId (if any) corresponding to this measurementId. If there is one, wait on\n      // that appId's initialization promise. If there is none, promise resolves and gtag\n      // call goes through.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      const foundConfig = dynamicConfigResults.find(\n        config => config.measurementId === measurementId\n      );\n      if (foundConfig) {\n        await initializationPromisesMap[foundConfig.appId];\n      }\n    }\n  } catch (e) {\n    logger.error(e);\n  }\n  gtagCore(GtagCommand.CONFIG, measurementId, gtagParams);\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'event' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementId GA Measurement ID to log event to.\n * @param gtagParams Params to log with this event.\n */\nasync function gtagOnEvent(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  try {\n    let initializationPromisesToWaitFor: Array<Promise<string>> = [];\n\n    // If there's a 'send_to' param, check if any ID specified matches\n    // an initializeIds() promise we are waiting for.\n    if (gtagParams && gtagParams['send_to']) {\n      let gaSendToList: string | string[] = gtagParams['send_to'];\n      // Make it an array if is isn't, so it can be dealt with the same way.\n      if (!Array.isArray(gaSendToList)) {\n        gaSendToList = [gaSendToList];\n      }\n      // Checking 'send_to' fields requires having all measurement ID results back from\n      // the dynamic config fetch.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      for (const sendToId of gaSendToList) {\n        // Any fetched dynamic measurement ID that matches this 'send_to' ID\n        const foundConfig = dynamicConfigResults.find(\n          config => config.measurementId === sendToId\n        );\n        const initializationPromise =\n          foundConfig && initializationPromisesMap[foundConfig.appId];\n        if (initializationPromise) {\n          initializationPromisesToWaitFor.push(initializationPromise);\n        } else {\n          // Found an item in 'send_to' that is not associated\n          // directly with an FID, possibly a group.  Empty this array,\n          // exit the loop early, and let it get populated below.\n          initializationPromisesToWaitFor = [];\n          break;\n        }\n      }\n    }\n\n    // This will be unpopulated if there was no 'send_to' field , or\n    // if not all entries in the 'send_to' field could be mapped to\n    // a FID. In these cases, wait on all pending initialization promises.\n    if (initializationPromisesToWaitFor.length === 0) {\n      /* eslint-disable-next-line @typescript-eslint/no-floating-promises */\n      initializationPromisesToWaitFor = Object.values(\n        initializationPromisesMap\n      );\n    }\n\n    // Run core gtag function with args after all relevant initialization\n    // promises have been resolved.\n    await Promise.all(initializationPromisesToWaitFor);\n    // Workaround for http://b/141370449 - third argument cannot be undefined.\n    gtagCore(GtagCommand.EVENT, measurementId, gtagParams || {});\n  } catch (e) {\n    logger.error(e);\n  }\n}\n\n/**\n * Wraps a standard gtag function with extra code to wait for completion of\n * relevant initialization promises before sending requests.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n */\nfunction wrapGtag(\n  gtagCore: Gtag,\n  /**\n   * Allows wrapped gtag calls to wait on whichever initialization promises are required,\n   * depending on the contents of the gtag params' `send_to` field, if any.\n   */\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  /**\n   * Wrapped gtag calls sometimes require all dynamic config fetches to have returned\n   * before determining what initialization promises (which include FIDs) to wait for.\n   */\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  /**\n   * Wrapped gtag config calls can narrow down which initialization promise (with FID)\n   * to wait for if the measurementId is already fetched, by getting the corresponding appId,\n   * which is the key for the initialization promises map.\n   */\n  measurementIdToAppId: { [measurementId: string]: string }\n): Gtag {\n  /**\n   * Wrapper around gtag that ensures FID is sent with gtag calls.\n   * @param command Gtag command type.\n   * @param idOrNameOrParams Measurement ID if command is EVENT/CONFIG, params if command is SET.\n   * @param gtagParams Params if event is EVENT/CONFIG.\n   */\n  async function gtagWrapper(\n    command: 'config' | 'set' | 'event' | 'consent' | 'get' | string,\n    ...args: unknown[]\n  ): Promise<void> {\n    try {\n      // If event, check that relevant initialization promises have completed.\n      if (command === GtagCommand.EVENT) {\n        const [measurementId, gtagParams] = args;\n        // If EVENT, second arg must be measurementId.\n        await gtagOnEvent(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONFIG) {\n        const [measurementId, gtagParams] = args;\n        // If CONFIG, second arg must be measurementId.\n        await gtagOnConfig(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementIdToAppId,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONSENT) {\n        const [consentAction, gtagParams] = args;\n        // consentAction can be one of 'default' or 'update'.\n        gtagCore(\n          GtagCommand.CONSENT,\n          consentAction,\n          gtagParams as ConsentSettings\n        );\n      } else if (command === GtagCommand.GET) {\n        const [measurementId, fieldName, callback] = args;\n        gtagCore(\n          GtagCommand.GET,\n          measurementId as string,\n          fieldName as string,\n          callback as (...args: unknown[]) => void\n        );\n      } else if (command === GtagCommand.SET) {\n        const [customParams] = args;\n        // If SET, second arg must be params.\n        gtagCore(GtagCommand.SET, customParams as CustomParams);\n      } else {\n        gtagCore(command, ...args);\n      }\n    } catch (e) {\n      logger.error(e);\n    }\n  }\n  return gtagWrapper as Gtag;\n}\n\n/**\n * Creates global gtag function or wraps existing one if found.\n * This wrapped function attaches Firebase instance ID (FID) to gtag 'config' and\n * 'event' calls that belong to the GAID associated with this Firebase instance.\n *\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param dataLayerName Name of global GA datalayer array.\n * @param gtagFunctionName Name of global gtag function (\"gtag\" if not user-specified).\n */\nexport function wrapOrCreateGtag(\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  dataLayerName: string,\n  gtagFunctionName: string\n): {\n  gtagCore: Gtag;\n  wrappedGtag: Gtag;\n} {\n  // Create a basic core gtag function\n  let gtagCore: Gtag = function (..._args: unknown[]) {\n    // Must push IArguments object, not an array.\n    (window[dataLayerName] as DataLayer).push(arguments);\n  };\n\n  // Replace it with existing one if found\n  if (\n    window[gtagFunctionName] &&\n    typeof window[gtagFunctionName] === 'function'\n  ) {\n    // @ts-ignore\n    gtagCore = window[gtagFunctionName];\n  }\n\n  window[gtagFunctionName] = wrapGtag(\n    gtagCore,\n    initializationPromisesMap,\n    dynamicConfigPromisesList,\n    measurementIdToAppId\n  );\n\n  return {\n    gtagCore,\n    wrappedGtag: window[gtagFunctionName] as Gtag\n  };\n}\n\n/**\n * Returns the script tag in the DOM matching both the gtag url pattern\n * and the provided data layer name.\n */\nexport function findGtagScriptOnPage(\n  dataLayerName: string\n): HTMLScriptElement | null {\n  const scriptTags = window.document.getElementsByTagName('script');\n  for (const tag of Object.values(scriptTags)) {\n    if (\n      tag.src &&\n      tag.src.includes(GTAG_URL) &&\n      tag.src.includes(dataLayerName)\n    ) {\n      return tag;\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Most logic is copied from packages/remote-config/src/client/retrying_client.ts\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { DynamicConfig, ThrottleMetadata, MinimalDynamicConfig } from './types';\nimport { FirebaseError, calculateBackoffMillis } from '@firebase/util';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { DYNAMIC_CONFIG_URL, FETCH_TIMEOUT_MILLIS } from './constants';\nimport { logger } from './logger';\n\n// App config fields needed by analytics.\nexport interface AppFields {\n  appId: string;\n  apiKey: string;\n  measurementId?: string;\n}\n\n/**\n * Backoff factor for 503 errors, which we want to be conservative about\n * to avoid overloading servers. Each retry interval will be\n * BASE_INTERVAL_MILLIS * LONG_RETRY_FACTOR ^ retryCount, so the second one\n * will be ~30 seconds (with fuzzing).\n */\nexport const LONG_RETRY_FACTOR = 30;\n\n/**\n * Base wait interval to multiplied by backoffFactor^backoffCount.\n */\nconst BASE_INTERVAL_MILLIS = 1000;\n\n/**\n * Stubbable retry data storage class.\n */\nclass RetryData {\n  constructor(\n    public throttleMetadata: { [appId: string]: ThrottleMetadata } = {},\n    public intervalMillis: number = BASE_INTERVAL_MILLIS\n  ) {}\n\n  getThrottleMetadata(appId: string): ThrottleMetadata {\n    return this.throttleMetadata[appId];\n  }\n\n  setThrottleMetadata(appId: string, metadata: ThrottleMetadata): void {\n    this.throttleMetadata[appId] = metadata;\n  }\n\n  deleteThrottleMetadata(appId: string): void {\n    delete this.throttleMetadata[appId];\n  }\n}\n\nconst defaultRetryData = new RetryData();\n\n/**\n * Set GET request headers.\n * @param apiKey App API key.\n */\nfunction getHeaders(apiKey: string): Headers {\n  return new Headers({\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\n/**\n * Fetches dynamic config from backend.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfig(\n  appFields: AppFields\n): Promise<DynamicConfig> {\n  const { appId, apiKey } = appFields;\n  const request: RequestInit = {\n    method: 'GET',\n    headers: getHeaders(apiKey)\n  };\n  const appUrl = DYNAMIC_CONFIG_URL.replace('{app-id}', appId);\n  const response = await fetch(appUrl, request);\n  if (response.status !== 200 && response.status !== 304) {\n    let errorMessage = '';\n    try {\n      // Try to get any error message text from server response.\n      const jsonResponse = (await response.json()) as {\n        error?: { message?: string };\n      };\n      if (jsonResponse.error?.message) {\n        errorMessage = jsonResponse.error.message;\n      }\n    } catch (_ignored) {}\n    throw ERROR_FACTORY.create(AnalyticsError.CONFIG_FETCH_FAILED, {\n      httpStatus: response.status,\n      responseMessage: errorMessage\n    });\n  }\n  return response.json();\n}\n\n/**\n * Fetches dynamic config from backend, retrying if failed.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfigWithRetry(\n  app: FirebaseApp,\n  // retryData and timeoutMillis are parameterized to allow passing a different value for testing.\n  retryData: RetryData = defaultRetryData,\n  timeoutMillis?: number\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, apiKey, measurementId } = app.options;\n\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n\n  if (!apiKey) {\n    if (measurementId) {\n      return {\n        measurementId,\n        appId\n      };\n    }\n    throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n  }\n\n  const throttleMetadata: ThrottleMetadata = retryData.getThrottleMetadata(\n    appId\n  ) || {\n    backoffCount: 0,\n    throttleEndTimeMillis: Date.now()\n  };\n\n  const signal = new AnalyticsAbortSignal();\n\n  setTimeout(\n    async () => {\n      // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n      signal.abort();\n    },\n    timeoutMillis !== undefined ? timeoutMillis : FETCH_TIMEOUT_MILLIS\n  );\n\n  return attemptFetchDynamicConfigWithRetry(\n    { appId, apiKey, measurementId },\n    throttleMetadata,\n    signal,\n    retryData\n  );\n}\n\n/**\n * Runs one retry attempt.\n * @param appFields Necessary app config fields.\n * @param throttleMetadata Ongoing metadata to determine throttling times.\n * @param signal Abort signal.\n */\nasync function attemptFetchDynamicConfigWithRetry(\n  appFields: AppFields,\n  { throttleEndTimeMillis, backoffCount }: ThrottleMetadata,\n  signal: AnalyticsAbortSignal,\n  retryData: RetryData = defaultRetryData // for testing\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, measurementId } = appFields;\n  // Starts with a (potentially zero) timeout to support resumption from stored state.\n  // Ensures the throttle end time is honored if the last attempt timed out.\n  // Note the SDK will never make a request if the fetch timeout expires at this point.\n  try {\n    await setAbortableTimeout(signal, throttleEndTimeMillis);\n  } catch (e) {\n    if (measurementId) {\n      logger.warn(\n        `Timed out fetching this Firebase app's measurement ID from the server.` +\n          ` Falling back to the measurement ID ${measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config. [${\n            (e as Error)?.message\n          }]`\n      );\n      return { appId, measurementId };\n    }\n    throw e;\n  }\n\n  try {\n    const response = await fetchDynamicConfig(appFields);\n\n    // Note the SDK only clears throttle state if response is success or non-retriable.\n    retryData.deleteThrottleMetadata(appId);\n\n    return response;\n  } catch (e) {\n    const error = e as Error;\n    if (!isRetriableError(error)) {\n      retryData.deleteThrottleMetadata(appId);\n      if (measurementId) {\n        logger.warn(\n          `Failed to fetch this Firebase app's measurement ID from the server.` +\n            ` Falling back to the measurement ID ${measurementId}` +\n            ` provided in the \"measurementId\" field in the local Firebase config. [${error?.message}]`\n        );\n        return { appId, measurementId };\n      } else {\n        throw e;\n      }\n    }\n\n    const backoffMillis =\n      Number(error?.customData?.httpStatus) === 503\n        ? calculateBackoffMillis(\n            backoffCount,\n            retryData.intervalMillis,\n            LONG_RETRY_FACTOR\n          )\n        : calculateBackoffMillis(backoffCount, retryData.intervalMillis);\n\n    // Increments backoff state.\n    const throttleMetadata = {\n      throttleEndTimeMillis: Date.now() + backoffMillis,\n      backoffCount: backoffCount + 1\n    };\n\n    // Persists state.\n    retryData.setThrottleMetadata(appId, throttleMetadata);\n    logger.debug(`Calling attemptFetch again in ${backoffMillis} millis`);\n\n    return attemptFetchDynamicConfigWithRetry(\n      appFields,\n      throttleMetadata,\n      signal,\n      retryData\n    );\n  }\n}\n\n/**\n * Supports waiting on a backoff by:\n *\n * <ul>\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\n *       request appear the same.</li>\n * </ul>\n *\n * <p>Visible for testing.\n */\nfunction setAbortableTimeout(\n  signal: AnalyticsAbortSignal,\n  throttleEndTimeMillis: number\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n\n    const timeout = setTimeout(resolve, backoffMillis);\n\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(\n        ERROR_FACTORY.create(AnalyticsError.FETCH_THROTTLE, {\n          throttleEndTimeMillis\n        })\n      );\n    });\n  });\n}\n\ntype RetriableError = FirebaseError & { customData: { httpStatus: string } };\n\n/**\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\n */\nfunction isRetriableError(e: Error): e is RetriableError {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\n/**\n * Shims a minimal AbortSignal (copied from Remote Config).\n *\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\n * swapped out if/when we do.\n */\nexport class AnalyticsAbortSignal {\n  listeners: Array<() => void> = [];\n  addEventListener(listener: () => void): void {\n    this.listeners.push(listener);\n  }\n  abort(): void {\n    this.listeners.forEach(listener => listener());\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AnalyticsCallOptions,\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { Gtag } from './types';\nimport { GtagCommand } from './constants';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n/**\n * Event parameters to set on 'gtag' during initialization.\n */\nexport let defaultEventParametersForInit: CustomParams | undefined;\n\n/**\n * Logs an analytics event through the Firebase SDK.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param eventName Google Analytics event name, choose from standard list or use a custom string.\n * @param eventParams Analytics event parameters.\n */\nexport async function logEvent(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.EVENT, eventName, eventParams);\n    return;\n  } else {\n    const measurementId = await initializationPromise;\n    const params: EventParams | ControlParams = {\n      ...eventParams,\n      'send_to': measurementId\n    };\n    gtagFunction(GtagCommand.EVENT, eventName, params);\n  }\n}\n\n/**\n * Set screen_name parameter for this Google Analytics ID.\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param screenName Screen name string to set.\n */\nexport async function setCurrentScreen(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  screenName: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'screen_name': screenName });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'screen_name': screenName\n    });\n  }\n}\n\n/**\n * Set user_id parameter for this Google Analytics ID.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param id User ID string to set\n */\nexport async function setUserId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'user_id': id });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_id': id\n    });\n  }\n}\n\n/**\n * Set all other user properties other than user_id and screen_name.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param properties Map of user properties to set\n */\nexport async function setUserProperties(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    const flatProperties: { [key: string]: unknown } = {};\n    for (const key of Object.keys(properties)) {\n      // use dot notation for merge behavior in gtag.js\n      flatProperties[`user_properties.${key}`] = properties[key];\n    }\n    gtagFunction(GtagCommand.SET, flatProperties);\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_properties': properties\n    });\n  }\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n */\nexport async function internalGetGoogleAnalyticsClientId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>\n): Promise<string> {\n  const measurementId = await initializationPromise;\n  return new Promise((resolve, reject) => {\n    gtagFunction(\n      GtagCommand.GET,\n      measurementId,\n      'client_id',\n      (clientId: string) => {\n        if (!clientId) {\n          reject(ERROR_FACTORY.create(AnalyticsError.NO_CLIENT_ID));\n        }\n        resolve(clientId);\n      }\n    );\n  });\n}\n\n/**\n * Set whether collection is enabled for this ID.\n *\n * @param enabled If true, collection is enabled for this ID.\n */\nexport async function setAnalyticsCollectionEnabled(\n  initializationPromise: Promise<string>,\n  enabled: boolean\n): Promise<void> {\n  const measurementId = await initializationPromise;\n  window[`ga-disable-${measurementId}`] = !enabled;\n}\n\n/**\n * Consent parameters to default to during 'gtag' initialization.\n */\nexport let defaultConsentSettingsForInit: ConsentSettings | undefined;\n\n/**\n * Sets the variable {@link defaultConsentSettingsForInit} for use in the initialization of\n * analytics.\n *\n * @param consentSettings Maps the applicable end user consent state for gtag.js.\n */\nexport function _setConsentDefaultForInit(\n  consentSettings?: ConsentSettings\n): void {\n  defaultConsentSettingsForInit = consentSettings;\n}\n\n/**\n * Sets the variable `defaultEventParametersForInit` for use in the initialization of\n * analytics.\n *\n * @param customParams Any custom params the user may pass to gtag.js.\n */\nexport function _setDefaultEventParametersForInit(\n  customParams?: CustomParams\n): void {\n  defaultEventParametersForInit = customParams;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DynamicConfig, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GA_FID_KEY, ORIGIN_KEY } from './constants';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { fetchDynamicConfigWithRetry } from './get-config';\nimport { logger } from './logger';\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { findGtagScriptOnPage, insertScriptTag } from './helpers';\nimport { AnalyticsSettings } from './public-types';\nimport {\n  defaultConsentSettingsForInit,\n  _setConsentDefaultForInit,\n  defaultEventParametersForInit,\n  _setDefaultEventParametersForInit\n} from './functions';\n\nasync function validateIndexedDB(): Promise<boolean> {\n  if (!isIndexedDBAvailable()) {\n    logger.warn(\n      ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n        errorInfo: 'IndexedDB is not available in this environment.'\n      }).message\n    );\n    return false;\n  } else {\n    try {\n      await validateIndexedDBOpenable();\n    } catch (e) {\n      logger.warn(\n        ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n          errorInfo: (e as Error)?.toString()\n        }).message\n      );\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Initialize the analytics instance in gtag.js by calling config command with fid.\n *\n * NOTE: We combine analytics initialization and setting fid together because we want fid to be\n * part of the `page_view` event that's sent during the initialization\n * @param app Firebase app\n * @param gtagCore The gtag function that's not wrapped.\n * @param dynamicConfigPromisesList Array of all dynamic config promises.\n * @param measurementIdToAppId Maps measurementID to appID.\n * @param installations _FirebaseInstallationsInternal instance.\n *\n * @returns Measurement ID.\n */\nexport async function _initializeAnalytics(\n  app: FirebaseApp,\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [key: string]: string },\n  installations: _FirebaseInstallationsInternal,\n  gtagCore: Gtag,\n  dataLayerName: string,\n  options?: AnalyticsSettings\n): Promise<string> {\n  const dynamicConfigPromise = fetchDynamicConfigWithRetry(app);\n  // Once fetched, map measurementIds to appId, for ease of lookup in wrapped gtag function.\n  dynamicConfigPromise\n    .then(config => {\n      measurementIdToAppId[config.measurementId] = config.appId;\n      if (\n        app.options.measurementId &&\n        config.measurementId !== app.options.measurementId\n      ) {\n        logger.warn(\n          `The measurement ID in the local Firebase config (${app.options.measurementId})` +\n            ` does not match the measurement ID fetched from the server (${config.measurementId}).` +\n            ` To ensure analytics events are always sent to the correct Analytics property,` +\n            ` update the` +\n            ` measurement ID field in the local config or remove it from the local config.`\n        );\n      }\n    })\n    .catch(e => logger.error(e));\n  // Add to list to track state of all dynamic config promises.\n  dynamicConfigPromisesList.push(dynamicConfigPromise);\n\n  const fidPromise: Promise<string | undefined> = validateIndexedDB().then(\n    envIsValid => {\n      if (envIsValid) {\n        return installations.getId();\n      } else {\n        return undefined;\n      }\n    }\n  );\n\n  const [dynamicConfig, fid] = await Promise.all([\n    dynamicConfigPromise,\n    fidPromise\n  ]);\n\n  // Detect if user has already put the gtag <script> tag on this page with the passed in\n  // data layer name.\n  if (!findGtagScriptOnPage(dataLayerName)) {\n    insertScriptTag(dataLayerName, dynamicConfig.measurementId);\n  }\n\n  // Detects if there are consent settings that need to be configured.\n  if (defaultConsentSettingsForInit) {\n    gtagCore(GtagCommand.CONSENT, 'default', defaultConsentSettingsForInit);\n    _setConsentDefaultForInit(undefined);\n  }\n\n  // This command initializes gtag.js and only needs to be called once for the entire web app,\n  // but since it is idempotent, we can call it multiple times.\n  // We keep it together with other initialization logic for better code structure.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (gtagCore as any)('js', new Date());\n  // User config added first. We don't want users to accidentally overwrite\n  // base Firebase config properties.\n  const configProperties: Record<string, unknown> = options?.config ?? {};\n\n  // guard against developers accidentally setting properties with prefix `firebase_`\n  configProperties[ORIGIN_KEY] = 'firebase';\n  configProperties.update = true;\n\n  if (fid != null) {\n    configProperties[GA_FID_KEY] = fid;\n  }\n\n  // It should be the first config command called on this GA-ID\n  // Initialize this GA-ID and set FID on it using the gtag config API.\n  // Note: This will trigger a page_view event unless 'send_page_view' is set to false in\n  // `configProperties`.\n  gtagCore(GtagCommand.CONFIG, dynamicConfig.measurementId, configProperties);\n\n  // Detects if there is data that will be set on every event logged from the SDK.\n  if (defaultEventParametersForInit) {\n    gtagCore(GtagCommand.SET, defaultEventParametersForInit);\n    _setDefaultEventParametersForInit(undefined);\n  }\n\n  return dynamicConfig.measurementId;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsOptions, Analytics, AnalyticsSettings } from './public-types';\nimport { Gtag, DynamicConfig, MinimalDynamicConfig } from './types';\nimport { getOrCreateDataLayer, wrapOrCreateGtag } from './helpers';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { areCookiesEnabled, isBrowserExtension } from '@firebase/util';\nimport { _initializeAnalytics } from './initialize-analytics';\nimport { logger } from './logger';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\n\n/**\n * Analytics Service class.\n */\nexport class AnalyticsService implements Analytics, _FirebaseService {\n  constructor(public app: FirebaseApp) {}\n  _delete(): Promise<void> {\n    delete initializationPromisesMap[this.app.options.appId!];\n    return Promise.resolve();\n  }\n}\n\n/**\n * Maps appId to full initialization promise. Wrapped gtag calls must wait on\n * all or some of these, depending on the call's `send_to` param and the status\n * of the dynamic config fetches (see below).\n */\nexport let initializationPromisesMap: {\n  [appId: string]: Promise<string>; // Promise contains measurement ID string.\n} = {};\n\n/**\n * List of dynamic config fetch promises. In certain cases, wrapped gtag calls\n * wait on all these to be complete in order to determine if it can selectively\n * wait for only certain initialization (FID) promises or if it must wait for all.\n */\nlet dynamicConfigPromisesList: Array<\n  Promise<DynamicConfig | MinimalDynamicConfig>\n> = [];\n\n/**\n * Maps fetched measurementIds to appId. Populated when the app's dynamic config\n * fetch completes. If already populated, gtag config calls can use this to\n * selectively wait for only this app's initialization promise (FID) instead of all\n * initialization promises.\n */\nconst measurementIdToAppId: { [measurementId: string]: string } = {};\n\n/**\n * Name for window global data layer array used by GA: defaults to 'dataLayer'.\n */\nlet dataLayerName: string = 'dataLayer';\n\n/**\n * Name for window global gtag function used by GA: defaults to 'gtag'.\n */\nlet gtagName: string = 'gtag';\n\n/**\n * Reproduction of standard gtag function or reference to existing\n * gtag function on window object.\n */\nlet gtagCoreFunction: Gtag;\n\n/**\n * Wrapper around gtag function that ensures FID is sent with all\n * relevant event and config calls.\n */\nexport let wrappedGtagFunction: Gtag;\n\n/**\n * Flag to ensure page initialization steps (creation or wrapping of\n * dataLayer and gtag script) are only run once per page load.\n */\nlet globalInitDone: boolean = false;\n\n/**\n * For testing\n * @internal\n */\nexport function resetGlobalVars(\n  newGlobalInitDone = false,\n  newInitializationPromisesMap = {},\n  newDynamicPromises = []\n): void {\n  globalInitDone = newGlobalInitDone;\n  initializationPromisesMap = newInitializationPromisesMap;\n  dynamicConfigPromisesList = newDynamicPromises;\n  dataLayerName = 'dataLayer';\n  gtagName = 'gtag';\n}\n\n/**\n * For testing\n * @internal\n */\nexport function getGlobalVars(): {\n  initializationPromisesMap: { [appId: string]: Promise<string> };\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >;\n} {\n  return {\n    initializationPromisesMap,\n    dynamicConfigPromisesList\n  };\n}\n\n/**\n * Configures Firebase Analytics to use custom `gtag` or `dataLayer` names.\n * Intended to be used if `gtag.js` script has been installed on\n * this page independently of Firebase Analytics, and is using non-default\n * names for either the `gtag` function or for `dataLayer`.\n * Must be called before calling `getAnalytics()` or it won't\n * have any effect.\n *\n * @public\n *\n * @param options - Custom gtag and dataLayer names.\n */\nexport function settings(options: SettingsOptions): void {\n  if (globalInitDone) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n  }\n  if (options.dataLayerName) {\n    dataLayerName = options.dataLayerName;\n  }\n  if (options.gtagName) {\n    gtagName = options.gtagName;\n  }\n}\n\n/**\n * Returns true if no environment mismatch is found.\n * If environment mismatches are found, throws an INVALID_ANALYTICS_CONTEXT\n * error that also lists details for each mismatch found.\n */\nfunction warnOnBrowserContextMismatch(): void {\n  const mismatchedEnvMessages = [];\n  if (isBrowserExtension()) {\n    mismatchedEnvMessages.push('This is a browser extension environment.');\n  }\n  if (!areCookiesEnabled()) {\n    mismatchedEnvMessages.push('Cookies are not available.');\n  }\n  if (mismatchedEnvMessages.length > 0) {\n    const details = mismatchedEnvMessages\n      .map((message, index) => `(${index + 1}) ${message}`)\n      .join(' ');\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_ANALYTICS_CONTEXT, {\n      errorInfo: details\n    });\n    logger.warn(err.message);\n  }\n}\n\n/**\n * Analytics instance factory.\n * @internal\n */\nexport function factory(\n  app: FirebaseApp,\n  installations: _FirebaseInstallationsInternal,\n  options?: AnalyticsSettings\n): AnalyticsService {\n  warnOnBrowserContextMismatch();\n  const appId = app.options.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n  if (!app.options.apiKey) {\n    if (app.options.measurementId) {\n      logger.warn(\n        `The \"apiKey\" field is empty in the local Firebase config. This is needed to fetch the latest` +\n          ` measurement ID for this Firebase app. Falling back to the measurement ID ${app.options.measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config.`\n      );\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n    }\n  }\n  if (initializationPromisesMap[appId] != null) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_EXISTS, {\n      id: appId\n    });\n  }\n\n  if (!globalInitDone) {\n    // Steps here should only be done once per page: creation or wrapping\n    // of dataLayer and global gtag function.\n\n    getOrCreateDataLayer(dataLayerName);\n\n    const { wrappedGtag, gtagCore } = wrapOrCreateGtag(\n      initializationPromisesMap,\n      dynamicConfigPromisesList,\n      measurementIdToAppId,\n      dataLayerName,\n      gtagName\n    );\n    wrappedGtagFunction = wrappedGtag;\n    gtagCoreFunction = gtagCore;\n\n    globalInitDone = true;\n  }\n  // Async but non-blocking.\n  // This map reflects the completion state of all promises for each appId.\n  initializationPromisesMap[appId] = _initializeAnalytics(\n    app,\n    dynamicConfigPromisesList,\n    measurementIdToAppId,\n    installations,\n    gtagCoreFunction,\n    dataLayerName,\n    options\n  );\n\n  const analyticsInstance: AnalyticsService = new AnalyticsService(app);\n\n  return analyticsInstance;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable camelcase */\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport {\n  Analytics,\n  AnalyticsCallOptions,\n  AnalyticsSettings,\n  ConsentSettings,\n  CustomParams,\n  EventNameString,\n  EventParams\n} from './public-types';\nimport { Provider } from '@firebase/component';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable,\n  areCookiesEnabled,\n  isBrowserExtension,\n  getModularInstance,\n  deepEqual\n} from '@firebase/util';\nimport { ANALYTICS_TYPE, GtagCommand } from './constants';\nimport {\n  AnalyticsService,\n  initializationPromisesMap,\n  wrappedGtagFunction\n} from './factory';\nimport { logger } from './logger';\nimport {\n  logEvent as internalLogEvent,\n  setCurrentScreen as internalSetCurrentScreen,\n  setUserId as internalSetUserId,\n  setUserProperties as internalSetUserProperties,\n  setAnalyticsCollectionEnabled as internalSetAnalyticsCollectionEnabled,\n  _setConsentDefaultForInit,\n  _setDefaultEventParametersForInit,\n  internalGetGoogleAnalyticsClientId\n} from './functions';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\n\nexport { settings } from './factory';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    [ANALYTICS_TYPE]: AnalyticsService;\n  }\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function getAnalytics(app: FirebaseApp = getApp()): Analytics {\n  app = getModularInstance(app);\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n\n  if (analyticsProvider.isInitialized()) {\n    return analyticsProvider.getImmediate();\n  }\n\n  return initializeAnalytics(app);\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function initializeAnalytics(\n  app: FirebaseApp,\n  options: AnalyticsSettings = {}\n): Analytics {\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n  if (analyticsProvider.isInitialized()) {\n    const existingInstance = analyticsProvider.getImmediate();\n    if (deepEqual(options, analyticsProvider.getOptions())) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n    }\n  }\n  const analyticsInstance = analyticsProvider.initialize({ options });\n  return analyticsInstance;\n}\n\n/**\n * This is a public static method provided to users that wraps four different checks:\n *\n * 1. Check if it's not a browser extension environment.\n * 2. Check if cookies are enabled in current browser.\n * 3. Check if IndexedDB is supported by the browser environment.\n * 4. Check if the current browser context is valid for using `IndexedDB.open()`.\n *\n * @public\n *\n */\nexport async function isSupported(): Promise<boolean> {\n  if (isBrowserExtension()) {\n    return false;\n  }\n  if (!areCookiesEnabled()) {\n    return false;\n  }\n  if (!isIndexedDBAvailable()) {\n    return false;\n  }\n\n  try {\n    const isDBOpenable: boolean = await validateIndexedDBOpenable();\n    return isDBOpenable;\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Use gtag `config` command to set `screen_name`.\n *\n * @public\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param screenName - Screen name to set.\n */\nexport function setCurrentScreen(\n  analyticsInstance: Analytics,\n  screenName: string,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetCurrentScreen(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    screenName,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport async function getGoogleAnalyticsClientId(\n  analyticsInstance: Analytics\n): Promise<string> {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  return internalGetGoogleAnalyticsClientId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!]\n  );\n}\n\n/**\n * Use gtag `config` command to set `user_id`.\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param id - User ID to set.\n */\nexport function setUserId(\n  analyticsInstance: Analytics,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    id,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Use gtag `config` command to set all params specified.\n *\n * @public\n */\nexport function setUserProperties(\n  analyticsInstance: Analytics,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserProperties(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    properties,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Sets whether Google Analytics collection is enabled for this app on this device.\n * Sets global `window['ga-disable-analyticsId'] = true;`\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param enabled - If true, enables collection, if false, disables it.\n */\nexport function setAnalyticsCollectionEnabled(\n  analyticsInstance: Analytics,\n  enabled: boolean\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetAnalyticsCollectionEnabled(\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    enabled\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Adds data that will be set on every event logged from the SDK, including automatic ones.\n * With gtag's \"set\" command, the values passed persist on the current page and are passed with\n * all subsequent events.\n * @public\n * @param customParams - Any custom params the user may pass to gtag.js.\n */\nexport function setDefaultEventParameters(customParams: CustomParams): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.SET, customParams);\n  } else {\n    _setDefaultEventParametersForInit(customParams);\n  }\n}\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_payment_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    payment_type?: EventParams['payment_type'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_shipping_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    shipping_tier?: EventParams['shipping_tier'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_to_cart' | 'add_to_wishlist' | 'remove_from_cart',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'begin_checkout',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'checkout_progress',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/exceptions\n * | Measure exceptions}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'exception',\n  eventParams?: {\n    description?: EventParams['description'];\n    fatal?: EventParams['fatal'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'generate_lead',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'login',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/views\n * | Page views}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'page_view',\n  eventParams?: {\n    page_title?: string;\n    page_location?: string;\n    page_path?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'purchase' | 'refund',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    transaction_id: EventParams['transaction_id'];\n    tax?: EventParams['tax'];\n    shipping?: EventParams['shipping'];\n    items?: EventParams['items'];\n    coupon?: EventParams['coupon'];\n    affiliation?: EventParams['affiliation'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See {@link https://firebase.google.com/docs/analytics/screenviews\n * | Track Screenviews}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'screen_view',\n  eventParams?: {\n    firebase_screen: EventParams['firebase_screen'];\n    firebase_screen_class: EventParams['firebase_screen_class'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'search' | 'view_search_results',\n  eventParams?: {\n    search_term?: EventParams['search_term'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_content',\n  eventParams?: {\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_item',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_promotion' | 'view_promotion',\n  eventParams?: {\n    items?: EventParams['items'];\n    promotion_id?: EventParams['promotion_id'];\n    promotion_name?: EventParams['promotion_name'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'set_checkout_option',\n  eventParams?: {\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'share',\n  eventParams?: {\n    method?: EventParams['method'];\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'sign_up',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'timing_complete',\n  eventParams?: {\n    name: string;\n    value: number;\n    event_category?: string;\n    event_label?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_cart' | 'view_item',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_item_list',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent<T extends string>(\n  analyticsInstance: Analytics,\n  eventName: CustomEventName<T>,\n  eventParams?: { [key: string]: any },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * List of official event parameters can be found in the gtag.js\n * reference documentation:\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n *\n * @public\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalLogEvent(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    eventName,\n    eventParams,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Any custom event name string not in the standard list of recommended\n * event names.\n * @public\n */\nexport type CustomEventName<T> = T extends EventNameString ? never : T;\n\n/**\n * Sets the applicable end user consent state for this web app across all gtag references once\n * Firebase Analytics is initialized.\n *\n * Use the {@link ConsentSettings} to specify individual consent type values. By default consent\n * types are set to \"granted\".\n * @public\n * @param consentSettings - Maps the applicable end user consent state for gtag.js.\n */\nexport function setConsent(consentSettings: ConsentSettings): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.CONSENT, 'update', consentSettings);\n  } else {\n    _setConsentDefaultForInit(consentSettings);\n  }\n}\n", "/**\n * The Firebase Analytics Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport { FirebaseAnalyticsInternal } from '@firebase/analytics-interop-types';\nimport { factory } from './factory';\nimport { ANALYTICS_TYPE } from './constants';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { logEvent } from './api';\nimport { name, version } from '../package.json';\nimport { AnalyticsCallOptions } from './public-types';\nimport '@firebase/installations';\n\ndeclare global {\n  interface Window {\n    [key: string]: unknown;\n  }\n}\n\nfunction registerAnalytics(): void {\n  _registerComponent(\n    new Component(\n      ANALYTICS_TYPE,\n      (container, { options: analyticsOptions }: InstanceFactoryOptions) => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const installations = container\n          .getProvider('installations-internal')\n          .getImmediate();\n\n        return factory(app, installations, analyticsOptions);\n      },\n      ComponentType.PUBLIC\n    )\n  );\n\n  _registerComponent(\n    new Component('analytics-internal', internalFactory, ComponentType.PRIVATE)\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n\n  function internalFactory(\n    container: ComponentContainer\n  ): FirebaseAnalyticsInternal {\n    try {\n      const analytics = container.getProvider(ANALYTICS_TYPE).getImmediate();\n      return {\n        logEvent: (\n          eventName: string,\n          eventParams?: { [key: string]: unknown },\n          options?: AnalyticsCallOptions\n        ) => logEvent(analytics, eventName, eventParams, options)\n      };\n    } catch (e) {\n      throw ERROR_FACTORY.create(AnalyticsError.INTEROP_COMPONENT_REG_FAILED, {\n        reason: e as Error\n      });\n    }\n  }\n}\n\nregisterAnalytics();\n\nexport * from './api';\nexport * from './public-types';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAmBO,IAAM,qBAAqB;AAE3B,IAAM,kBAAkB,KAAK,OAAO;AACpC,IAAM,wBAAwB;AAE9B,IAAM,wBACX;AAEK,IAAM,0BAA0B,KAAK,KAAK;AAE1C,IAAM,UAAU;AAChB,IAAM,eAAe;ACD5B,IAAM,wBAAiE;EACrE;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GAA4B;EAC5B;IAAA;;EAAA,GAAoC;EACpC;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GAAyB;EACzB;IAAA;;EAAA,GACE;;AAaG,IAAM,gBAAgB,IAAI,aAC/B,SACA,cACA,qBAAqB;AAYjB,SAAU,cAAc,OAAc;AAC1C,SACE,iBAAiB,iBACjB,MAAM,KAAK;IAAQ;;EAAA;AAEvB;ACxCgB,SAAA,yBAAyB,EAAE,UAAS,GAAa;AAC/D,SAAO,GAAG,qBAAqB,aAAa,SAAS;AACvD;AAEM,SAAU,iCACd,UAAmC;AAEnC,SAAO;IACL,OAAO,SAAS;IAChB,eAAsC;IACtC,WAAW,kCAAkC,SAAS,SAAS;IAC/D,cAAc,KAAK,IAAG;;AAE1B;AAEO,eAAe,qBACpB,aACA,UAAkB;AAElB,QAAM,eAA8B,MAAM,SAAS,KAAI;AACvD,QAAM,YAAY,aAAa;AAC/B,SAAO,cAAc,OAAiC,kBAAA;IACpD;IACA,YAAY,UAAU;IACtB,eAAe,UAAU;IACzB,cAAc,UAAU;EACzB,CAAA;AACH;AAEgB,SAAA,WAAW,EAAE,OAAM,GAAa;AAC9C,SAAO,IAAI,QAAQ;IACjB,gBAAgB;IAChB,QAAQ;IACR,kBAAkB;EACnB,CAAA;AACH;SAEgB,mBACd,WACA,EAAE,aAAY,GAA+B;AAE7C,QAAM,UAAU,WAAW,SAAS;AACpC,UAAQ,OAAO,iBAAiB,uBAAuB,YAAY,CAAC;AACpE,SAAO;AACT;AAeO,eAAe,mBACpB,IAA2B;AAE3B,QAAM,SAAS,MAAM,GAAE;AAEvB,MAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;AAE/C,WAAO,GAAE;EACV;AAED,SAAO;AACT;AAEA,SAAS,kCAAkC,mBAAyB;AAElE,SAAO,OAAO,kBAAkB,QAAQ,KAAK,KAAK,CAAC;AACrD;AAEA,SAAS,uBAAuB,cAAoB;AAClD,SAAO,GAAG,qBAAqB,IAAI,YAAY;AACjD;AC7EO,eAAe,0BACpB,EAAE,WAAW,yBAAwB,GACrC,EAAE,IAAG,GAA+B;AAEpC,QAAM,WAAW,yBAAyB,SAAS;AAEnD,QAAM,UAAU,WAAW,SAAS;AAGpC,QAAM,mBAAmB,yBAAyB,aAAa;IAC7D,UAAU;EACX,CAAA;AACD,MAAI,kBAAkB;AACpB,UAAM,mBAAmB,MAAM,iBAAiB,oBAAmB;AACnE,QAAI,kBAAkB;AACpB,cAAQ,OAAO,qBAAqB,gBAAgB;IACrD;EACF;AAED,QAAM,OAAO;IACX;IACA,aAAa;IACb,OAAO,UAAU;IACjB,YAAY;;AAGd,QAAM,UAAuB;IAC3B,QAAQ;IACR;IACA,MAAM,KAAK,UAAU,IAAI;;AAG3B,QAAM,WAAW,MAAM,mBAAmB,MAAM,MAAM,UAAU,OAAO,CAAC;AACxE,MAAI,SAAS,IAAI;AACf,UAAM,gBAA4C,MAAM,SAAS,KAAI;AACrE,UAAM,8BAA2D;MAC/D,KAAK,cAAc,OAAO;MAC1B,oBAA2C;MAC3C,cAAc,cAAc;MAC5B,WAAW,iCAAiC,cAAc,SAAS;;AAErE,WAAO;EACR,OAAM;AACL,UAAM,MAAM,qBAAqB,uBAAuB,QAAQ;EACjE;AACH;AC5DM,SAAU,MAAM,IAAU;AAC9B,SAAO,IAAI,QAAc,aAAU;AACjC,eAAW,SAAS,EAAE;EACxB,CAAC;AACH;ACLM,SAAU,sBAAsB,OAAiB;AACrD,QAAM,MAAM,KAAK,OAAO,aAAa,GAAG,KAAK,CAAC;AAC9C,SAAO,IAAI,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACnD;ACDO,IAAM,oBAAoB;AAC1B,IAAM,cAAc;SAMX,cAAW;AACzB,MAAI;AAGF,UAAM,eAAe,IAAI,WAAW,EAAE;AACtC,UAAM,SACJ,KAAK,UAAW,KAAyC;AAC3D,WAAO,gBAAgB,YAAY;AAGnC,iBAAa,CAAC,IAAI,MAAc,aAAa,CAAC,IAAI;AAElD,UAAM,MAAM,OAAO,YAAY;AAE/B,WAAO,kBAAkB,KAAK,GAAG,IAAI,MAAM;EAC5C,SAAO,IAAA;AAEN,WAAO;EACR;AACH;AAGA,SAAS,OAAO,cAAwB;AACtC,QAAM,YAAY,sBAAsB,YAAY;AAIpD,SAAO,UAAU,OAAO,GAAG,EAAE;AAC/B;AClCM,SAAU,OAAO,WAAoB;AACzC,SAAO,GAAG,UAAU,OAAO,IAAI,UAAU,KAAK;AAChD;ACDA,IAAM,qBAA2D,oBAAI,IAAG;AAMxD,SAAA,WAAW,WAAsB,KAAW;AAC1D,QAAM,MAAM,OAAO,SAAS;AAE5B,yBAAuB,KAAK,GAAG;AAC/B,qBAAmB,KAAK,GAAG;AAC7B;AAyCA,SAAS,uBAAuB,KAAa,KAAW;AACtD,QAAM,YAAY,mBAAmB,IAAI,GAAG;AAC5C,MAAI,CAAC,WAAW;AACd;EACD;AAED,aAAW,YAAY,WAAW;AAChC,aAAS,GAAG;EACb;AACH;AAEA,SAAS,mBAAmB,KAAa,KAAW;AAClD,QAAM,UAAU,oBAAmB;AACnC,MAAI,SAAS;AACX,YAAQ,YAAY,EAAE,KAAK,IAAG,CAAE;EACjC;AACD,wBAAqB;AACvB;AAEA,IAAI,mBAA4C;AAEhD,SAAS,sBAAmB;AAC1B,MAAI,CAAC,oBAAoB,sBAAsB,MAAM;AACnD,uBAAmB,IAAI,iBAAiB,uBAAuB;AAC/D,qBAAiB,YAAY,OAAI;AAC/B,6BAAuB,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;IAC/C;EACD;AACD,SAAO;AACT;AAEA,SAAS,wBAAqB;AAC5B,MAAI,mBAAmB,SAAS,KAAK,kBAAkB;AACrD,qBAAiB,MAAK;AACtB,uBAAmB;EACpB;AACH;ACtFA,IAAM,gBAAgB;AACtB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAS1B,IAAI,YAA2D;AAC/D,SAAS,eAAY;AACnB,MAAI,CAAC,WAAW;AACd,gBAAY,OAAO,eAAe,kBAAkB;MAClD,SAAS,CAAC,IAAI,eAAc;AAM1B,gBAAQ,YAAU;UAChB,KAAK;AACH,eAAG,kBAAkB,iBAAiB;QACzC;;IAEJ,CAAA;EACF;AACD,SAAO;AACT;AAeO,eAAe,IACpB,WACA,OAAgB;AAEhB,QAAM,MAAM,OAAO,SAAS;AAC5B,QAAM,KAAK,MAAM,aAAY;AAC7B,QAAM,KAAK,GAAG,YAAY,mBAAmB,WAAW;AACxD,QAAM,cAAc,GAAG,YAAY,iBAAiB;AACpD,QAAM,WAAY,MAAM,YAAY,IAAI,GAAG;AAC3C,QAAM,YAAY,IAAI,OAAO,GAAG;AAChC,QAAM,GAAG;AAET,MAAI,CAAC,YAAY,SAAS,QAAQ,MAAM,KAAK;AAC3C,eAAW,WAAW,MAAM,GAAG;EAChC;AAED,SAAO;AACT;AAGO,eAAe,OAAO,WAAoB;AAC/C,QAAM,MAAM,OAAO,SAAS;AAC5B,QAAM,KAAK,MAAM,aAAY;AAC7B,QAAM,KAAK,GAAG,YAAY,mBAAmB,WAAW;AACxD,QAAM,GAAG,YAAY,iBAAiB,EAAE,OAAO,GAAG;AAClD,QAAM,GAAG;AACX;AAQO,eAAe,OACpB,WACA,UAAqE;AAErE,QAAM,MAAM,OAAO,SAAS;AAC5B,QAAM,KAAK,MAAM,aAAY;AAC7B,QAAM,KAAK,GAAG,YAAY,mBAAmB,WAAW;AACxD,QAAM,QAAQ,GAAG,YAAY,iBAAiB;AAC9C,QAAM,WAA2C,MAAM,MAAM,IAC3D,GAAG;AAEL,QAAM,WAAW,SAAS,QAAQ;AAElC,MAAI,aAAa,QAAW;AAC1B,UAAM,MAAM,OAAO,GAAG;EACvB,OAAM;AACL,UAAM,MAAM,IAAI,UAAU,GAAG;EAC9B;AACD,QAAM,GAAG;AAET,MAAI,aAAa,CAAC,YAAY,SAAS,QAAQ,SAAS,MAAM;AAC5D,eAAW,WAAW,SAAS,GAAG;EACnC;AAED,SAAO;AACT;AClFO,eAAe,qBACpB,eAAwC;AAExC,MAAI;AAEJ,QAAM,oBAAoB,MAAM,OAAO,cAAc,WAAW,cAAW;AACzE,UAAMA,qBAAoB,gCAAgC,QAAQ;AAClE,UAAM,mBAAmB,+BACvB,eACAA,kBAAiB;AAEnB,0BAAsB,iBAAiB;AACvC,WAAO,iBAAiB;EAC1B,CAAC;AAED,MAAI,kBAAkB,QAAQ,aAAa;AAEzC,WAAO,EAAE,mBAAmB,MAAM,oBAAoB;EACvD;AAED,SAAO;IACL;IACA;;AAEJ;AAMA,SAAS,gCACP,UAAuC;AAEvC,QAAM,QAA2B,YAAY;IAC3C,KAAK,YAAW;IAChB,oBAA6C;;;AAG/C,SAAO,qBAAqB,KAAK;AACnC;AASA,SAAS,+BACP,eACA,mBAAoC;AAEpC,MAAI,kBAAkB,uBAAkB,GAAgC;AACtE,QAAI,CAAC,UAAU,QAAQ;AAErB,YAAM,+BAA+B,QAAQ,OAC3C,cAAc;QAA6B;;MAAA,CAAA;AAE7C,aAAO;QACL;QACA,qBAAqB;;IAExB;AAGD,UAAM,kBAA+C;MACnD,KAAK,kBAAkB;MACvB,oBAA6C;MAC7C,kBAAkB,KAAK,IAAG;;AAE5B,UAAM,sBAAsB,qBAC1B,eACA,eAAe;AAEjB,WAAO,EAAE,mBAAmB,iBAAiB,oBAAmB;EACjE,WACC,kBAAkB,uBAAkB,GACpC;AACA,WAAO;MACL;MACA,qBAAqB,yBAAyB,aAAa;;EAE9D,OAAM;AACL,WAAO,EAAE,kBAAiB;EAC3B;AACH;AAGA,eAAe,qBACb,eACA,mBAA8C;AAE9C,MAAI;AACF,UAAM,8BAA8B,MAAM,0BACxC,eACA,iBAAiB;AAEnB,WAAO,IAAI,cAAc,WAAW,2BAA2B;EAChE,SAAQ,GAAG;AACV,QAAI,cAAc,CAAC,KAAK,EAAE,WAAW,eAAe,KAAK;AAGvD,YAAM,OAAO,cAAc,SAAS;IACrC,OAAM;AAEL,YAAM,IAAI,cAAc,WAAW;QACjC,KAAK,kBAAkB;QACvB,oBAA6C;;MAC9C,CAAA;IACF;AACD,UAAM;EACP;AACH;AAGA,eAAe,yBACb,eAAwC;AAMxC,MAAI,QAA2B,MAAM,0BACnC,cAAc,SAAS;AAEzB,SAAO,MAAM,uBAAkB,GAAgC;AAE7D,UAAM,MAAM,GAAG;AAEf,YAAQ,MAAM,0BAA0B,cAAc,SAAS;EAChE;AAED,MAAI,MAAM,uBAAkB,GAAgC;AAE1D,UAAM,EAAE,mBAAmB,oBAAmB,IAC5C,MAAM,qBAAqB,aAAa;AAE1C,QAAI,qBAAqB;AACvB,aAAO;IACR,OAAM;AAEL,aAAO;IACR;EACF;AAED,SAAO;AACT;AAUA,SAAS,0BACP,WAAoB;AAEpB,SAAO,OAAO,WAAW,cAAW;AAClC,QAAI,CAAC,UAAU;AACb,YAAM,cAAc;QAAM;;MAAA;IAC3B;AACD,WAAO,qBAAqB,QAAQ;EACtC,CAAC;AACH;AAEA,SAAS,qBAAqB,OAAwB;AACpD,MAAI,+BAA+B,KAAK,GAAG;AACzC,WAAO;MACL,KAAK,MAAM;MACX,oBAA6C;;;EAEhD;AAED,SAAO;AACT;AAEA,SAAS,+BACP,mBAAoC;AAEpC,SACE,kBAAkB,uBAAgD,KAClE,kBAAkB,mBAAmB,qBAAqB,KAAK,IAAG;AAEtE;AClMO,eAAe,yBACpB,EAAE,WAAW,yBAAwB,GACrC,mBAA8C;AAE9C,QAAM,WAAW,6BAA6B,WAAW,iBAAiB;AAE1E,QAAM,UAAU,mBAAmB,WAAW,iBAAiB;AAG/D,QAAM,mBAAmB,yBAAyB,aAAa;IAC7D,UAAU;EACX,CAAA;AACD,MAAI,kBAAkB;AACpB,UAAM,mBAAmB,MAAM,iBAAiB,oBAAmB;AACnE,QAAI,kBAAkB;AACpB,cAAQ,OAAO,qBAAqB,gBAAgB;IACrD;EACF;AAED,QAAM,OAAO;IACX,cAAc;MACZ,YAAY;MACZ,OAAO,UAAU;IAClB;;AAGH,QAAM,UAAuB;IAC3B,QAAQ;IACR;IACA,MAAM,KAAK,UAAU,IAAI;;AAG3B,QAAM,WAAW,MAAM,mBAAmB,MAAM,MAAM,UAAU,OAAO,CAAC;AACxE,MAAI,SAAS,IAAI;AACf,UAAM,gBAA2C,MAAM,SAAS,KAAI;AACpE,UAAM,qBACJ,iCAAiC,aAAa;AAChD,WAAO;EACR,OAAM;AACL,UAAM,MAAM,qBAAqB,uBAAuB,QAAQ;EACjE;AACH;AAEA,SAAS,6BACP,WACA,EAAE,IAAG,GAA+B;AAEpC,SAAO,GAAG,yBAAyB,SAAS,CAAC,IAAI,GAAG;AACtD;AC1CO,eAAe,iBACpB,eACA,eAAe,OAAK;AAEpB,MAAI;AACJ,QAAM,QAAQ,MAAM,OAAO,cAAc,WAAW,cAAW;AAC7D,QAAI,CAAC,kBAAkB,QAAQ,GAAG;AAChC,YAAM,cAAc;QAAM;;MAAA;IAC3B;AAED,UAAM,eAAe,SAAS;AAC9B,QAAI,CAAC,gBAAgB,iBAAiB,YAAY,GAAG;AAEnD,aAAO;IACR,WAAU,aAAa,kBAAa,GAAgC;AAEnE,qBAAe,0BAA0B,eAAe,YAAY;AACpE,aAAO;IACR,OAAM;AAEL,UAAI,CAAC,UAAU,QAAQ;AACrB,cAAM,cAAc;UAAM;;QAAA;MAC3B;AAED,YAAM,kBAAkB,oCAAoC,QAAQ;AACpE,qBAAe,yBAAyB,eAAe,eAAe;AACtE,aAAO;IACR;EACH,CAAC;AAED,QAAM,YAAY,eACd,MAAM,eACL,MAAM;AACX,SAAO;AACT;AAQA,eAAe,0BACb,eACA,cAAqB;AAMrB,MAAI,QAAQ,MAAM,uBAAuB,cAAc,SAAS;AAChE,SAAO,MAAM,UAAU,kBAAa,GAAgC;AAElE,UAAM,MAAM,GAAG;AAEf,YAAQ,MAAM,uBAAuB,cAAc,SAAS;EAC7D;AAED,QAAM,YAAY,MAAM;AACxB,MAAI,UAAU,kBAAa,GAAgC;AAEzD,WAAO,iBAAiB,eAAe,YAAY;EACpD,OAAM;AACL,WAAO;EACR;AACH;AAUA,SAAS,uBACP,WAAoB;AAEpB,SAAO,OAAO,WAAW,cAAW;AAClC,QAAI,CAAC,kBAAkB,QAAQ,GAAG;AAChC,YAAM,cAAc;QAAM;;MAAA;IAC3B;AAED,UAAM,eAAe,SAAS;AAC9B,QAAI,4BAA4B,YAAY,GAAG;AAC7C,aACK,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,QAAQ,GAAA,EACX,WAAW;QAAE,eAAa;;MAAA,EAA6B,CACvD;IACH;AAED,WAAO;EACT,CAAC;AACH;AAEA,eAAe,yBACb,eACA,mBAA8C;AAE9C,MAAI;AACF,UAAM,YAAY,MAAM,yBACtB,eACA,iBAAiB;AAEnB,UAAM,2BACD,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,iBAAiB,GACpB,EAAA,UAAS,CAAA;AAEX,UAAM,IAAI,cAAc,WAAW,wBAAwB;AAC3D,WAAO;EACR,SAAQ,GAAG;AACV,QACE,cAAc,CAAC,MACd,EAAE,WAAW,eAAe,OAAO,EAAE,WAAW,eAAe,MAChE;AAGA,YAAM,OAAO,cAAc,SAAS;IACrC,OAAM;AACL,YAAM,2BACD,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,iBAAiB,GACpB,EAAA,WAAW;QAAE,eAAa;;MAAA,EAA6B,CAAA;AAEzD,YAAM,IAAI,cAAc,WAAW,wBAAwB;IAC5D;AACD,UAAM;EACP;AACH;AAEA,SAAS,kBACP,mBAAgD;AAEhD,SACE,sBAAsB,UACtB,kBAAkB,uBAA8C;AAEpE;AAEA,SAAS,iBAAiB,WAAoB;AAC5C,SACE,UAAU,kBAAyC,KACnD,CAAC,mBAAmB,SAAS;AAEjC;AAEA,SAAS,mBAAmB,WAA6B;AACvD,QAAM,MAAM,KAAK,IAAG;AACpB,SACE,MAAM,UAAU,gBAChB,UAAU,eAAe,UAAU,YAAY,MAAM;AAEzD;AAGA,SAAS,oCACP,UAAqC;AAErC,QAAM,sBAA2C;IAC/C,eAAwC;IACxC,aAAa,KAAK,IAAG;;AAEvB,SAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,QAAQ,GAAA,EACX,WAAW,oBAAmB,CAC9B;AACJ;AAEA,SAAS,4BAA4B,WAAoB;AACvD,SACE,UAAU,kBAA2C,KACrD,UAAU,cAAc,qBAAqB,KAAK,IAAG;AAEzD;ACxLO,eAAe,MAAM,eAA4B;AACtD,QAAM,oBAAoB;AAC1B,QAAM,EAAE,mBAAmB,oBAAmB,IAAK,MAAM,qBACvD,iBAAiB;AAGnB,MAAI,qBAAqB;AACvB,wBAAoB,MAAM,QAAQ,KAAK;EACxC,OAAM;AAGL,qBAAiB,iBAAiB,EAAE,MAAM,QAAQ,KAAK;EACxD;AAED,SAAO,kBAAkB;AAC3B;ACdO,eAAe,SACpB,eACA,eAAe,OAAK;AAEpB,QAAM,oBAAoB;AAC1B,QAAM,iCAAiC,iBAAiB;AAIxD,QAAM,YAAY,MAAM,iBAAiB,mBAAmB,YAAY;AACxE,SAAO,UAAU;AACnB;AAEA,eAAe,iCACb,eAAwC;AAExC,QAAM,EAAE,oBAAmB,IAAK,MAAM,qBAAqB,aAAa;AAExE,MAAI,qBAAqB;AAEvB,UAAM;EACP;AACH;AK9BM,SAAU,iBAAiB,KAAgB;AAC/C,MAAI,CAAC,OAAO,CAAC,IAAI,SAAS;AACxB,UAAM,qBAAqB,mBAAmB;EAC/C;AAED,MAAI,CAAC,IAAI,MAAM;AACb,UAAM,qBAAqB,UAAU;EACtC;AAGD,QAAM,aAA2C;IAC/C;IACA;IACA;;AAGF,aAAW,WAAW,YAAY;AAChC,QAAI,CAAC,IAAI,QAAQ,OAAO,GAAG;AACzB,YAAM,qBAAqB,OAAO;IACnC;EACF;AAED,SAAO;IACL,SAAS,IAAI;IACb,WAAW,IAAI,QAAQ;IACvB,QAAQ,IAAI,QAAQ;IACpB,OAAO,IAAI,QAAQ;;AAEvB;AAEA,SAAS,qBAAqB,WAAiB;AAC7C,SAAO,cAAc,OAA4C,6BAAA;IAC/D;EACD,CAAA;AACH;AC3BA,IAAM,qBAAqB;AAC3B,IAAM,8BAA8B;AAEpC,IAAM,gBAAkD,CACtD,cACE;AACF,QAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAY;AAErD,QAAM,YAAY,iBAAiB,GAAG;AACtC,QAAM,2BAA2B,aAAa,KAAK,WAAW;AAE9D,QAAM,oBAA+C;IACnD;IACA;IACA;IACA,SAAS,MAAM,QAAQ,QAAO;;AAEhC,SAAO;AACT;AAEA,IAAM,kBAA6D,CACjE,cACE;AACF,QAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAY;AAErD,QAAM,gBAAgB,aAAa,KAAK,kBAAkB,EAAE,aAAY;AAExE,QAAM,wBAAwD;IAC5D,OAAO,MAAM,MAAM,aAAa;IAChC,UAAU,CAAC,iBAA2B,SAAS,eAAe,YAAY;;AAE5E,SAAO;AACT;SAEgB,wBAAqB;AACnC,qBACE,IAAI;IAAU;IAAoB;IAAoC;;EAAA,CAAA;AAExE,qBACE,IAAI;IACF;IACA;IAED;;EAAA,CAAA;AAEL;AC3CA,sBAAqB;AACrB,gBAAgB,MAAM,OAAO;AAE7B,gBAAgB,MAAM,SAAS,SAAkB;;;ACd1C,IAAM,iBAAiB;AAGvB,IAAM,aAAa;AACnB,IAAM,aAAa;AAEnB,IAAM,uBAAuB,KAAK;AAElC,IAAM,qBACX;AAEK,IAAM,WAAW;ACZjB,IAAM,SAAS,IAAI,OAAO,qBAAqB;ACetD,IAAM,SAAmC;EACvC;IAAA;;EAAA,GACE;EAGF;IAAA;;EAAA,GACE;EAIF;IAAA;;EAAA,GACE;EAGF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EAGF;IAAA;;EAAA,GACE;EAGF;IAAA;;EAAA,GACE;EAEF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EAEF;IAAA;;EAAA,GACE;EAEF;IAAA;;EAAA,GAA+B;EAC/B;IAAA;;EAAA,GACE;;AAgBG,IAAMC,iBAAgB,IAAI,aAC/B,aACA,aACA,MAAM;ACxDF,SAAU,gCAAgC,KAAW;AACzD,MAAI,CAAC,IAAI,WAAW,QAAQ,GAAG;AAC7B,UAAM,MAAMA,eAAc,OAA6C,yBAAA;MACrE,SAAS;IACV,CAAA;AACD,WAAO,KAAK,IAAI,OAAO;AACvB,WAAO;EACR;AACD,SAAO;AACT;AAQM,SAAU,kBACd,UAA2B;AAE3B,SAAO,QAAQ,IAAI,SAAS,IAAI,aAAW,QAAQ,MAAM,OAAK,CAAC,CAAC,CAAC;AACnE;AASgB,SAAA,yBACd,YACA,eAAgD;AAIhD,MAAI;AACJ,MAAI,OAAO,cAAc;AACvB,yBAAqB,OAAO,aAAa,aACvC,YACA,aAAa;EAEhB;AACD,SAAO;AACT;AAMgB,SAAA,gBACdC,gBACA,eAAqB;AAErB,QAAM,qBAAqB,yBACzB,0BACA;IACE,iBAAiB;EAClB,CAAA;AAGH,QAAM,SAAS,SAAS,cAAc,QAAQ;AAI9C,QAAM,gBAAgB,GAAG,QAAQ,MAAMA,cAAa,OAAO,aAAa;AACvE,SAAO,MAAoC,qBACvC,uBAAwC,QAAxC,uBAAkB,SAAA,SAAlB,mBAA0C,gBAAgB,aAAa,IACxE;AAEJ,SAAO,QAAQ;AACf,WAAS,KAAK,YAAY,MAAM;AAClC;AAMM,SAAU,qBAAqBA,gBAAqB;AAExD,MAAI,YAAuB,CAAA;AAC3B,MAAI,MAAM,QAAQ,OAAOA,cAAa,CAAC,GAAG;AACxC,gBAAY,OAAOA,cAAa;EACjC,OAAM;AACL,WAAOA,cAAa,IAAI;EACzB;AACD,SAAO;AACT;AAYA,eAAe,aACb,UACAC,4BACAC,4BAGAC,uBACA,eACA,YAAuD;AAIvD,QAAM,qBAAqBA,sBAAqB,aAAuB;AACvE,MAAI;AACF,QAAI,oBAAoB;AACtB,YAAMF,2BAA0B,kBAAkB;IACnD,OAAM;AAKL,YAAM,uBAAuB,MAAM,kBACjCC,0BAAyB;AAE3B,YAAM,cAAc,qBAAqB,KACvC,YAAU,OAAO,kBAAkB,aAAa;AAElD,UAAI,aAAa;AACf,cAAMD,2BAA0B,YAAY,KAAK;MAClD;IACF;EACF,SAAQ,GAAG;AACV,WAAO,MAAM,CAAC;EACf;AACD,WAA6B,UAAA,eAAe,UAAU;AACxD;AAWA,eAAe,YACb,UACAA,4BACAC,4BAGA,eACA,YAAuD;AAEvD,MAAI;AACF,QAAI,kCAA0D,CAAA;AAI9D,QAAI,cAAc,WAAW,SAAS,GAAG;AACvC,UAAI,eAAkC,WAAW,SAAS;AAE1D,UAAI,CAAC,MAAM,QAAQ,YAAY,GAAG;AAChC,uBAAe,CAAC,YAAY;MAC7B;AAGD,YAAM,uBAAuB,MAAM,kBACjCA,0BAAyB;AAE3B,iBAAW,YAAY,cAAc;AAEnC,cAAM,cAAc,qBAAqB,KACvC,YAAU,OAAO,kBAAkB,QAAQ;AAE7C,cAAM,wBACJ,eAAeD,2BAA0B,YAAY,KAAK;AAC5D,YAAI,uBAAuB;AACzB,0CAAgC,KAAK,qBAAqB;QAC3D,OAAM;AAIL,4CAAkC,CAAA;AAClC;QACD;MACF;IACF;AAKD,QAAI,gCAAgC,WAAW,GAAG;AAEhD,wCAAkC,OAAO,OACvCA,0BAAyB;IAE5B;AAID,UAAM,QAAQ,IAAI,+BAA+B;AAEjD,aAAQ,SAAoB,eAAe,cAAc,CAAA,CAAE;EAC5D,SAAQ,GAAG;AACV,WAAO,MAAM,CAAC;EACf;AACH;AAWA,SAAS,SACP,UAKAA,4BAKAC,4BAQAC,uBAAyD;AAQzD,iBAAe,YACb,YACG,MAAe;AAElB,QAAI;AAEF,UAAI,YAAO,SAAwB;AACjC,cAAM,CAAC,eAAe,UAAU,IAAI;AAEpC,cAAM,YACJ,UACAF,4BACAC,4BACA,eACA,UAAqC;MAExC,WAAU,YAAO,UAAyB;AACzC,cAAM,CAAC,eAAe,UAAU,IAAI;AAEpC,cAAM,aACJ,UACAD,4BACAC,4BACAC,uBACA,eACA,UAAqC;MAExC,WAAU,YAAO,WAA0B;AAC1C,cAAM,CAAC,eAAe,UAAU,IAAI;AAEpC,iBAEE,WAAA,eACA,UAA6B;MAEhC,WAAU,YAAO,OAAsB;AACtC,cAAM,CAAC,eAAe,WAAW,QAAQ,IAAI;AAC7C,iBAAQ,OAEN,eACA,WACA,QAAwC;MAE3C,WAAU,YAAO,OAAsB;AACtC,cAAM,CAAC,YAAY,IAAI;AAEvB,iBAAQ,OAAkB,YAA4B;MACvD,OAAM;AACL,iBAAS,SAAS,GAAG,IAAI;MAC1B;IACF,SAAQ,GAAG;AACV,aAAO,MAAM,CAAC;IACf;;AAEH,SAAO;AACT;AAaM,SAAU,iBACdF,4BACAC,4BAGAC,uBACAH,gBACA,kBAAwB;AAMxB,MAAI,WAAiB,YAAa,OAAgB;AAE/C,WAAOA,cAAa,EAAgB,KAAK,SAAS;EACrD;AAGA,MACE,OAAO,gBAAgB,KACvB,OAAO,OAAO,gBAAgB,MAAM,YACpC;AAEA,eAAW,OAAO,gBAAgB;EACnC;AAED,SAAO,gBAAgB,IAAI,SACzB,UACAC,4BACAC,4BACAC,qBAAoB;AAGtB,SAAO;IACL;IACA,aAAa,OAAO,gBAAgB;;AAExC;AAMM,SAAU,qBACdH,gBAAqB;AAErB,QAAM,aAAa,OAAO,SAAS,qBAAqB,QAAQ;AAChE,aAAW,OAAO,OAAO,OAAO,UAAU,GAAG;AAC3C,QACE,IAAI,OACJ,IAAI,IAAI,SAAS,QAAQ,KACzB,IAAI,IAAI,SAASA,cAAa,GAC9B;AACA,aAAO;IACR;EACF;AACD,SAAO;AACT;AC5WO,IAAM,oBAAoB;AAKjC,IAAM,uBAAuB;AAK7B,IAAM,YAAN,MAAe;EACb,YACS,mBAA0D,CAAA,GAC1D,iBAAyB,sBAAoB;AAD7C,SAAgB,mBAAhB;AACA,SAAc,iBAAd;;EAGT,oBAAoB,OAAa;AAC/B,WAAO,KAAK,iBAAiB,KAAK;;EAGpC,oBAAoB,OAAe,UAA0B;AAC3D,SAAK,iBAAiB,KAAK,IAAI;;EAGjC,uBAAuB,OAAa;AAClC,WAAO,KAAK,iBAAiB,KAAK;;AAErC;AAED,IAAM,mBAAmB,IAAI,UAAS;AAMtC,SAASI,YAAW,QAAc;AAChC,SAAO,IAAI,QAAQ;IACjB,QAAQ;IACR,kBAAkB;EACnB,CAAA;AACH;AAMO,eAAe,mBACpB,WAAoB;;AAEpB,QAAM,EAAE,OAAO,OAAM,IAAK;AAC1B,QAAM,UAAuB;IAC3B,QAAQ;IACR,SAASA,YAAW,MAAM;;AAE5B,QAAM,SAAS,mBAAmB,QAAQ,YAAY,KAAK;AAC3D,QAAM,WAAW,MAAM,MAAM,QAAQ,OAAO;AAC5C,MAAI,SAAS,WAAW,OAAO,SAAS,WAAW,KAAK;AACtD,QAAI,eAAe;AACnB,QAAI;AAEF,YAAM,eAAgB,MAAM,SAAS,KAAI;AAGzC,WAAI,KAAA,aAAa,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;AAC/B,uBAAe,aAAa,MAAM;MACnC;IACF,SAAQ,UAAU;IAAA;AACnB,UAAML,eAAc,OAA2C,uBAAA;MAC7D,YAAY,SAAS;MACrB,iBAAiB;IAClB,CAAA;EACF;AACD,SAAO,SAAS,KAAI;AACtB;AAMO,eAAe,4BACpB,KAEA,YAAuB,kBACvB,eAAsB;AAEtB,QAAM,EAAE,OAAO,QAAQ,cAAa,IAAK,IAAI;AAE7C,MAAI,CAAC,OAAO;AACV,UAAMA,eAAc;MAAM;;IAAA;EAC3B;AAED,MAAI,CAAC,QAAQ;AACX,QAAI,eAAe;AACjB,aAAO;QACL;QACA;;IAEH;AACD,UAAMA,eAAc;MAAM;;IAAA;EAC3B;AAED,QAAM,mBAAqC,UAAU,oBACnD,KAAK,KACF;IACH,cAAc;IACd,uBAAuB,KAAK,IAAG;;AAGjC,QAAM,SAAS,IAAI,qBAAoB;AAEvC,aACE,YAAW;AAET,WAAO,MAAK;EACd,GACA,kBAAkB,SAAY,gBAAgB,oBAAoB;AAGpE,SAAO,mCACL,EAAE,OAAO,QAAQ,cAAa,GAC9B,kBACA,QACA,SAAS;AAEb;AAQA,eAAe,mCACb,WACA,EAAE,uBAAuB,aAAY,GACrC,QACA,YAAuB;;AAEvB,QAAM,EAAE,OAAO,cAAa,IAAK;AAIjC,MAAI;AACF,UAAM,oBAAoB,QAAQ,qBAAqB;EACxD,SAAQ,GAAG;AACV,QAAI,eAAe;AACjB,aAAO,KACL,6GACyC,aAAa,yEAEjD,MAAW,QAAX,MAAC,SAAA,SAAD,EAAa,OAChB,GAAG;AAEP,aAAO,EAAE,OAAO,cAAa;IAC9B;AACD,UAAM;EACP;AAED,MAAI;AACF,UAAM,WAAW,MAAM,mBAAmB,SAAS;AAGnD,cAAU,uBAAuB,KAAK;AAEtC,WAAO;EACR,SAAQ,GAAG;AACV,UAAM,QAAQ;AACd,QAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,gBAAU,uBAAuB,KAAK;AACtC,UAAI,eAAe;AACjB,eAAO,KACL,0GACyC,aAAa,yEACqB,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,OAAO,GAAG;AAE9F,eAAO,EAAE,OAAO,cAAa;MAC9B,OAAM;AACL,cAAM;MACP;IACF;AAED,UAAM,gBACJ,QAAO,KAAA,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,UAAU,MAAM,MACtC,uBACE,cACA,UAAU,gBACV,iBAAiB,IAEnB,uBAAuB,cAAc,UAAU,cAAc;AAGnE,UAAM,mBAAmB;MACvB,uBAAuB,KAAK,IAAG,IAAK;MACpC,cAAc,eAAe;;AAI/B,cAAU,oBAAoB,OAAO,gBAAgB;AACrD,WAAO,MAAM,iCAAiC,aAAa,SAAS;AAEpE,WAAO,mCACL,WACA,kBACA,QACA,SAAS;EAEZ;AACH;AAcA,SAAS,oBACP,QACA,uBAA6B;AAE7B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AAErC,UAAM,gBAAgB,KAAK,IAAI,wBAAwB,KAAK,IAAG,GAAI,CAAC;AAEpE,UAAM,UAAU,WAAW,SAAS,aAAa;AAGjD,WAAO,iBAAiB,MAAK;AAC3B,mBAAa,OAAO;AAEpB,aACEA,eAAc,OAAsC,kBAAA;QAClD;MACD,CAAA,CAAC;IAEN,CAAC;EACH,CAAC;AACH;AAOA,SAAS,iBAAiB,GAAQ;AAChC,MAAI,EAAE,aAAa,kBAAkB,CAAC,EAAE,YAAY;AAClD,WAAO;EACR;AAGD,QAAM,aAAa,OAAO,EAAE,WAAW,YAAY,CAAC;AAEpD,SACE,eAAe,OACf,eAAe,OACf,eAAe,OACf,eAAe;AAEnB;IAUa,6BAAoB;EAAjC,cAAA;AACE,SAAS,YAAsB,CAAA;;EAC/B,iBAAiB,UAAoB;AACnC,SAAK,UAAU,KAAK,QAAQ;;EAE9B,QAAK;AACH,SAAK,UAAU,QAAQ,cAAY,SAAQ,CAAE;;AAEhD;ACnSM,IAAI;AASJ,eAAeM,WACpB,cACA,uBACA,WACA,aACA,SAA8B;AAE9B,MAAI,WAAW,QAAQ,QAAQ;AAC7B,iBAAgC,SAAA,WAAW,WAAW;AACtD;EACD,OAAM;AACL,UAAM,gBAAgB,MAAM;AAC5B,UAAM,SAAM,OAAA,OAAA,OAAA,OAAA,CAAA,GACP,WAAW,GAAA,EACd,WAAW,cAAa,CAAA;AAE1B,iBAAgC,SAAA,WAAW,MAAM;EAClD;AACH;AAWO,eAAeC,mBACpB,cACA,uBACA,YACA,SAA8B;AAE9B,MAAI,WAAW,QAAQ,QAAQ;AAC7B,iBAAY,OAAkB,EAAE,eAAe,WAAU,CAAE;AAC3D,WAAO,QAAQ,QAAO;EACvB,OAAM;AACL,UAAM,gBAAgB,MAAM;AAC5B,iBAAY,UAAqB,eAAe;MAC9C,QAAQ;MACR,eAAe;IAChB,CAAA;EACF;AACH;AAQO,eAAeC,YACpB,cACA,uBACA,IACA,SAA8B;AAE9B,MAAI,WAAW,QAAQ,QAAQ;AAC7B,iBAAY,OAAkB,EAAE,WAAW,GAAE,CAAE;AAC/C,WAAO,QAAQ,QAAO;EACvB,OAAM;AACL,UAAM,gBAAgB,MAAM;AAC5B,iBAAY,UAAqB,eAAe;MAC9C,QAAQ;MACR,WAAW;IACZ,CAAA;EACF;AACH;AAQO,eAAeC,oBACpB,cACA,uBACA,YACA,SAA8B;AAE9B,MAAI,WAAW,QAAQ,QAAQ;AAC7B,UAAM,iBAA6C,CAAA;AACnD,eAAW,OAAO,OAAO,KAAK,UAAU,GAAG;AAEzC,qBAAe,mBAAmB,GAAG,EAAE,IAAI,WAAW,GAAG;IAC1D;AACD,iBAAY,OAAkB,cAAc;AAC5C,WAAO,QAAQ,QAAO;EACvB,OAAM;AACL,UAAM,gBAAgB,MAAM;AAC5B,iBAAY,UAAqB,eAAe;MAC9C,QAAQ;MACR,mBAAmB;IACpB,CAAA;EACF;AACH;AAQO,eAAe,mCACpB,cACA,uBAAsC;AAEtC,QAAM,gBAAgB,MAAM;AAC5B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,iBAAY,OAEV,eACA,aACA,CAAC,aAAoB;AACnB,UAAI,CAAC,UAAU;AACb,eAAOT,eAAc;UAAM;;QAAA,CAA6B;MACzD;AACD,cAAQ,QAAQ;IAClB,CAAC;EAEL,CAAC;AACH;AAOO,eAAeU,gCACpB,uBACA,SAAgB;AAEhB,QAAM,gBAAgB,MAAM;AAC5B,SAAO,cAAc,aAAa,EAAE,IAAI,CAAC;AAC3C;AAKO,IAAI;AAQL,SAAU,0BACd,iBAAiC;AAEjC,kCAAgC;AAClC;AAQM,SAAU,kCACd,cAA2B;AAE3B,kCAAgC;AAClC;ACzKA,eAAe,oBAAiB;AAC9B,MAAI,CAAC,qBAAoB,GAAI;AAC3B,WAAO,KACLV,eAAc,OAA6C,yBAAA;MACzD,WAAW;KACZ,EAAE,OAAO;AAEZ,WAAO;EACR,OAAM;AACL,QAAI;AACF,YAAM,0BAAyB;IAChC,SAAQ,GAAG;AACV,aAAO,KACLA,eAAc,OAA6C,yBAAA;QACzD,WAAY,MAAA,QAAA,MAAC,SAAA,SAAD,EAAa,SAAQ;OAClC,EAAE,OAAO;AAEZ,aAAO;IACR;EACF;AACD,SAAO;AACT;AAeO,eAAe,qBACpB,KACAG,4BAGAC,uBACA,eACA,UACAH,gBACA,SAA2B;;AAE3B,QAAM,uBAAuB,4BAA4B,GAAG;AAE5D,uBACG,KAAK,YAAS;AACb,IAAAG,sBAAqB,OAAO,aAAa,IAAI,OAAO;AACpD,QACE,IAAI,QAAQ,iBACZ,OAAO,kBAAkB,IAAI,QAAQ,eACrC;AACA,aAAO,KACL,oDAAoD,IAAI,QAAQ,aAAa,gEACZ,OAAO,aAAa,0KAGJ;IAEpF;EACH,CAAC,EACA,MAAM,OAAK,OAAO,MAAM,CAAC,CAAC;AAE7B,EAAAD,2BAA0B,KAAK,oBAAoB;AAEnD,QAAM,aAA0C,kBAAiB,EAAG,KAClE,gBAAa;AACX,QAAI,YAAY;AACd,aAAO,cAAc,MAAK;IAC3B,OAAM;AACL,aAAO;IACR;EACH,CAAC;AAGH,QAAM,CAAC,eAAe,GAAG,IAAI,MAAM,QAAQ,IAAI;IAC7C;IACA;EACD,CAAA;AAID,MAAI,CAAC,qBAAqBF,cAAa,GAAG;AACxC,oBAAgBA,gBAAe,cAAc,aAAa;EAC3D;AAGD,MAAI,+BAA+B;AACjC,aAA8B,WAAA,WAAW,6BAA6B;AACtE,8BAA0B,MAAS;EACpC;AAMA,WAAiB,MAAM,oBAAI,KAAI,CAAE;AAGlC,QAAM,oBAA4C,KAAA,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,YAAU,QAAA,OAAA,SAAA,KAAA,CAAA;AAGrE,mBAAiB,UAAU,IAAI;AAC/B,mBAAiB,SAAS;AAE1B,MAAI,OAAO,MAAM;AACf,qBAAiB,UAAU,IAAI;EAChC;AAMD,WAAQ,UAAqB,cAAc,eAAe,gBAAgB;AAG1E,MAAI,+BAA+B;AACjC,aAAQ,OAAkB,6BAA6B;AACvD,sCAAkC,MAAS;EAC5C;AAED,SAAO,cAAc;AACvB;ICrIa,yBAAgB;EAC3B,YAAmB,KAAgB;AAAhB,SAAG,MAAH;;EACnB,UAAO;AACL,WAAO,0BAA0B,KAAK,IAAI,QAAQ,KAAM;AACxD,WAAO,QAAQ,QAAO;;AAEzB;AAOM,IAAI,4BAEP,CAAA;AAOJ,IAAI,4BAEA,CAAA;AAQJ,IAAM,uBAA4D,CAAA;AAKlE,IAAI,gBAAwB;AAK5B,IAAI,WAAmB;AAMvB,IAAI;AAMG,IAAI;AAMX,IAAI,iBAA0B;AA8CxB,SAAU,SAAS,SAAwB;AAC/C,MAAI,gBAAgB;AAClB,UAAMD,eAAc;MAAM;;IAAA;EAC3B;AACD,MAAI,QAAQ,eAAe;AACzB,oBAAgB,QAAQ;EACzB;AACD,MAAI,QAAQ,UAAU;AACpB,eAAW,QAAQ;EACpB;AACH;AAOA,SAAS,+BAA4B;AACnC,QAAM,wBAAwB,CAAA;AAC9B,MAAI,mBAAkB,GAAI;AACxB,0BAAsB,KAAK,0CAA0C;EACtE;AACD,MAAI,CAAC,kBAAiB,GAAI;AACxB,0BAAsB,KAAK,4BAA4B;EACxD;AACD,MAAI,sBAAsB,SAAS,GAAG;AACpC,UAAM,UAAU,sBACb,IAAI,CAAC,SAAS,UAAU,IAAI,QAAQ,CAAC,KAAK,OAAO,EAAE,EACnD,KAAK,GAAG;AACX,UAAM,MAAMA,eAAc,OAAiD,6BAAA;MACzE,WAAW;IACZ,CAAA;AACD,WAAO,KAAK,IAAI,OAAO;EACxB;AACH;SAMgB,QACd,KACA,eACA,SAA2B;AAE3B,+BAA4B;AAC5B,QAAM,QAAQ,IAAI,QAAQ;AAC1B,MAAI,CAAC,OAAO;AACV,UAAMA,eAAc;MAAM;;IAAA;EAC3B;AACD,MAAI,CAAC,IAAI,QAAQ,QAAQ;AACvB,QAAI,IAAI,QAAQ,eAAe;AAC7B,aAAO,KACL,yKAC+E,IAAI,QAAQ,aAAa,sEAChC;IAE3E,OAAM;AACL,YAAMA,eAAc;QAAM;;MAAA;IAC3B;EACF;AACD,MAAI,0BAA0B,KAAK,KAAK,MAAM;AAC5C,UAAMA,eAAc,OAAsC,kBAAA;MACxD,IAAI;IACL,CAAA;EACF;AAED,MAAI,CAAC,gBAAgB;AAInB,yBAAqB,aAAa;AAElC,UAAM,EAAE,aAAa,SAAQ,IAAK,iBAChC,2BACA,2BACA,sBACA,eACA,QAAQ;AAEV,0BAAsB;AACtB,uBAAmB;AAEnB,qBAAiB;EAClB;AAGD,4BAA0B,KAAK,IAAI,qBACjC,KACA,2BACA,sBACA,eACA,kBACA,eACA,OAAO;AAGT,QAAM,oBAAsC,IAAI,iBAAiB,GAAG;AAEpE,SAAO;AACT;ACpKgB,SAAA,aAAa,MAAmB,OAAM,GAAE;AACtD,QAAM,mBAAmB,GAAG;AAE5B,QAAM,oBAA2C,aAC/C,KACA,cAAc;AAGhB,MAAI,kBAAkB,cAAa,GAAI;AACrC,WAAO,kBAAkB,aAAY;EACtC;AAED,SAAO,oBAAoB,GAAG;AAChC;SASgB,oBACd,KACA,UAA6B,CAAA,GAAE;AAG/B,QAAM,oBAA2C,aAC/C,KACA,cAAc;AAEhB,MAAI,kBAAkB,cAAa,GAAI;AACrC,UAAM,mBAAmB,kBAAkB,aAAY;AACvD,QAAI,UAAU,SAAS,kBAAkB,WAAU,CAAE,GAAG;AACtD,aAAO;IACR,OAAM;AACL,YAAMA,eAAc;QAAM;;MAAA;IAC3B;EACF;AACD,QAAM,oBAAoB,kBAAkB,WAAW,EAAE,QAAO,CAAE;AAClE,SAAO;AACT;AAaO,eAAe,cAAW;AAC/B,MAAI,mBAAkB,GAAI;AACxB,WAAO;EACR;AACD,MAAI,CAAC,kBAAiB,GAAI;AACxB,WAAO;EACR;AACD,MAAI,CAAC,qBAAoB,GAAI;AAC3B,WAAO;EACR;AAED,MAAI;AACF,UAAM,eAAwB,MAAM,0BAAyB;AAC7D,WAAO;EACR,SAAQ,OAAO;AACd,WAAO;EACR;AACH;SAagB,iBACd,mBACA,YACA,SAA8B;AAE9B,sBAAoB,mBAAmB,iBAAiB;AACxDW,qBACE,qBACA,0BAA0B,kBAAkB,IAAI,QAAQ,KAAM,GAC9D,YACA,OAAO,EACP,MAAM,OAAK,OAAO,MAAM,CAAC,CAAC;AAC9B;AAUO,eAAe,2BACpB,mBAA4B;AAE5B,sBAAoB,mBAAmB,iBAAiB;AACxD,SAAO,mCACL,qBACA,0BAA0B,kBAAkB,IAAI,QAAQ,KAAM,CAAC;AAEnE;SAUgB,UACd,mBACA,IACA,SAA8B;AAE9B,sBAAoB,mBAAmB,iBAAiB;AACxDC,cACE,qBACA,0BAA0B,kBAAkB,IAAI,QAAQ,KAAM,GAC9D,IACA,OAAO,EACP,MAAM,OAAK,OAAO,MAAM,CAAC,CAAC;AAC9B;SAOgB,kBACd,mBACA,YACA,SAA8B;AAE9B,sBAAoB,mBAAmB,iBAAiB;AACxDC,sBACE,qBACA,0BAA0B,kBAAkB,IAAI,QAAQ,KAAM,GAC9D,YACA,OAAO,EACP,MAAM,OAAK,OAAO,MAAM,CAAC,CAAC;AAC9B;AAWgB,SAAA,8BACd,mBACA,SAAgB;AAEhB,sBAAoB,mBAAmB,iBAAiB;AACxDC,kCACE,0BAA0B,kBAAkB,IAAI,QAAQ,KAAM,GAC9D,OAAO,EACP,MAAM,OAAK,OAAO,MAAM,CAAC,CAAC;AAC9B;AASM,SAAU,0BAA0B,cAA0B;AAElE,MAAI,qBAAqB;AACvB,wBAAmB,OAAkB,YAAY;EAClD,OAAM;AACL,sCAAkC,YAAY;EAC/C;AACH;AAwdM,SAAU,SACd,mBACA,WACA,aACA,SAA8B;AAE9B,sBAAoB,mBAAmB,iBAAiB;AACxDC,aACE,qBACA,0BAA0B,kBAAkB,IAAI,QAAQ,KAAM,GAC9D,WACA,aACA,OAAO,EACP,MAAM,OAAK,OAAO,MAAM,CAAC,CAAC;AAC9B;AAkBM,SAAU,WAAW,iBAAgC;AAEzD,MAAI,qBAAqB;AACvB,wBAAyC,WAAA,UAAU,eAAe;EACnE,OAAM;AACL,8BAA0B,eAAe;EAC1C;AACH;;;ACxtBA,SAAS,oBAAiB;AACxB,qBACE,IAAI;IACF;IACA,CAAC,WAAW,EAAE,SAAS,iBAAgB,MAA8B;AAEnE,YAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAY;AACrD,YAAM,gBAAgB,UACnB,YAAY,wBAAwB,EACpC,aAAY;AAEf,aAAO,QAAQ,KAAK,eAAe,gBAAgB;;IACpD;;EAAA,CAEF;AAGH,qBACE,IAAI;IAAU;IAAsBC;IAAuC;;EAAA,CAAA;AAG7E,kBAAgBC,OAAMC,QAAO;AAE7B,kBAAgBD,OAAMC,UAAS,SAAkB;AAEjD,WAASF,iBACP,WAA6B;AAE7B,QAAI;AACF,YAAM,YAAY,UAAU,YAAY,cAAc,EAAE,aAAY;AACpE,aAAO;QACL,UAAU,CACR,WACA,aACA,YACG,SAAS,WAAW,WAAW,aAAa,OAAO;;IAE3D,SAAQ,GAAG;AACV,YAAMhB,eAAc,OAAoD,gCAAA;QACtE,QAAQ;MACT,CAAA;IACF;;AAEL;AAEA,kBAAiB;", "names": ["installationEntry", "ERROR_FACTORY", "dataLayerName", "initializationPromisesMap", "dynamicConfigPromisesList", "measurementIdToAppId", "getHeaders", "logEvent", "setCurrentScreen", "setUserId", "setUserProperties", "setAnalyticsCollectionEnabled", "internalSetCurrentScreen", "internalSetUserId", "internalSetUserProperties", "internalSetAnalyticsCollectionEnabled", "internalLogEvent", "internalFactory", "name", "version"]}