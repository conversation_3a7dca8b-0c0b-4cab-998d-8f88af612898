{"hash": "a0a06061", "configHash": "5199e722", "lockfileHash": "1f607991", "browserHash": "e170c582", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "ddb89c9d", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "3805239c", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "1f916373", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "66b8f333", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "3c2832d7", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "060b012b", "needsInterop": false}, "firebase/analytics": {"src": "../../firebase/analytics/dist/esm/index.esm.js", "file": "firebase_analytics.js", "fileHash": "d7e91638", "needsInterop": false}, "firebase/app": {"src": "../../firebase/app/dist/esm/index.esm.js", "file": "firebase_app.js", "fileHash": "d217902e", "needsInterop": false}, "firebase/auth": {"src": "../../firebase/auth/dist/esm/index.esm.js", "file": "firebase_auth.js", "fileHash": "e7b9f50a", "needsInterop": false}, "firebase/firestore": {"src": "../../firebase/firestore/dist/esm/index.esm.js", "file": "firebase_firestore.js", "fileHash": "6fe6343a", "needsInterop": false}, "firebase/storage": {"src": "../../firebase/storage/dist/esm/index.esm.js", "file": "firebase_storage.js", "fileHash": "3aeeefd3", "needsInterop": false}, "i18next": {"src": "../../i18next/dist/esm/i18next.js", "file": "i18next.js", "fileHash": "a9643a99", "needsInterop": false}, "i18next-browser-languagedetector": {"src": "../../i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js", "file": "i18next-browser-languagedetector.js", "fileHash": "f9fd4995", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "684c9a5e", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "e0d63df0", "needsInterop": false}, "react-i18next": {"src": "../../react-i18next/dist/es/index.js", "file": "react-i18next.js", "fileHash": "fca97017", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "5f495ec4", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "fdfc0544", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "b1148f6b", "needsInterop": false}, "zustand/middleware/immer": {"src": "../../zustand/esm/middleware/immer.mjs", "file": "zustand_middleware_immer.js", "fileHash": "77e8cd3f", "needsInterop": false}}, "chunks": {"chunk-TYILIMWK": {"file": "chunk-TYILIMWK.js"}, "chunk-NZACC4LT": {"file": "chunk-NZACC4LT.js"}, "chunk-CANBAPAS": {"file": "chunk-CANBAPAS.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}