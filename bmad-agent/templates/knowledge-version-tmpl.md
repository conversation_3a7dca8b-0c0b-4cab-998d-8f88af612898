# Knowledge Version History

## Current Version: {VERSION_NUMBER}
**Last Updated:** [AGENT: Use current date in YYYY-MM-DD format]
**Updated By:** {AGENT_NAME}
**Change Type:** {MAJOR|MINOR|PATCH}

**IMPORTANT:** When using this template, replace {DATE} placeholders with actual current dates in YYYY-MM-DD format. Never leave date placeholders in final documents.

## Change Summary
{BRIEF_DESCRIPTION_OF_CHANGES}

## File Updates
{LIST_OF_FILES_CHANGED_WITH_SUMMARY}

## Details

### Added
- {NEW_INFORMATION_ITEM_1}
- {NEW_INFORMATION_ITEM_2}
- {NEW_INFORMATION_ITEM_N}

### Changed
- {MODIFIED_INFORMATION_ITEM_1}
  - From: {OLD_VALUE}
  - To: {NEW_VALUE}
- {MODIFIED_INFORMATION_ITEM_2}
  - From: {OLD_VALUE}
  - To: {NEW_VALUE}

### Removed
- {REMOVED_INFORMATION_ITEM_1}
- {REMOVED_INFORMATION_ITEM_2}

## Impact Analysis
- **Development:** {IMPACT_ON_DEVELOPMENT}
- **Testing:** {IMPACT_ON_TESTING}
- **Deployment:** {IMPACT_ON_DEPLOYMENT}
- **Timeline:** {IMPACT_ON_PROJECT_TIMELINE}

## Version History

| Version | Date | Updated By | Change Type | Summary |
|---------|------|------------|-------------|---------|
| {VERSION_NUMBER} | [AGENT: Current date YYYY-MM-DD] | {AGENT_NAME} | {CHANGE_TYPE} | {BRIEF_SUMMARY} |
| {PREVIOUS_VERSION} | [AGENT: Previous date YYYY-MM-DD] | {AGENT_NAME} | {CHANGE_TYPE} | {BRIEF_SUMMARY} |
| {INITIAL_VERSION} | [AGENT: Initial date YYYY-MM-DD] | {AGENT_NAME} | Initial | Initial knowledge base creation |