# Project Context

## Project Overview
- **Name:** {PROJECT_NAME}
- **Description:** {PROJECT_DESCRIPTION}
- **Goals:** {PROJECT_GOALS}
- **Timeline:** {PROJECT_TIMELINE}
- **Primary Users:** {TARGET_USERS}

## Key Terminology
- **{TERM_1}:** {DEFINITION_1}
- **{TERM_2}:** {DEFINITION_2}
- **{TERM_N}:** {DEFINITION_N}

## Domain Knowledge
{DOMAIN_SPECIFIC_INFORMATION}

## Project Constraints
- **Technical Constraints:** {TECHNICAL_CONSTRAINTS}
- **Business Constraints:** {BUSINESS_CONSTRAINTS}
- **Timeline Constraints:** {TIMELINE_CONSTRAINTS}
- **Resource Constraints:** {RESOURCE_CONSTRAINTS}

## Team Structure
- **Product Owner:** {PO_NAME/ROLE}
- **Development Team:** {DEV_TEAM_COMPOSITION}
- **Stakeholders:** {KEY_STAKEHOLDERS}

## Success Criteria
- {SUCCESS_CRITERION_1}
- {SUCCESS_CRITERION_2}
- {SUCCESS_CRITERION_N}