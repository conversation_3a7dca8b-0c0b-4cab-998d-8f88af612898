# QA Testing Checklist

## Test Planning & Strategy
- [ ] Test plan is aligned with story/feature requirements
- [ ] Test scope is clearly defined and appropriate
- [ ] Test environments are identified and available
- [ ] Appropriate test types are selected (unit, integration, E2E, etc.)
- [ ] Dependencies and prerequisites are identified
- [ ] Edge cases and potential failure modes are addressed

## Test Case Design
- [ ] Test cases cover all acceptance criteria
- [ ] Test data requirements are identified
- [ ] Positive and negative test cases are included
- [ ] Edge cases are covered with specific test cases
- [ ] Performance considerations are addressed if applicable
- [ ] Security considerations are addressed if applicable

## Test Automation
- [ ] Automated tests follow project standards and patterns
- [ ] Tests are maintainable and properly organized
- [ ] Test naming is clear and descriptive
- [ ] Tests are independent and don't rely on other tests
- [ ] Test assertions are meaningful and specific
- [ ] Test coverage meets project standards

## Test Execution & Reporting
- [ ] All planned tests have been executed
- [ ] Test results are documented clearly
- [ ] Defects are properly logged with clear reproduction steps
- [ ] Regression tests passed successfully
- [ ] Test metrics are captured and reported
- [ ] Issues are prioritized appropriately

## Quality Gate Assessment
- [ ] All critical and high-priority defects are resolved
- [ ] Code quality meets project standards
- [ ] Security standards are met
- [ ] Performance requirements are satisfied
- [ ] Documentation is complete and accurate
- [ ] Overall quality assessment is documented